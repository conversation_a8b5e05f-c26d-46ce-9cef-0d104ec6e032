import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../../core/base/controllers/base_controller.dart/base_controller.dart';
import '../../../../../models/categorias_menu.dart';
import '../../../../../models/story_model.dart';
import '../../../controllers/offers_controller.dart';
import '../../../controllers/products_details_controller.dart';
import '../../../controllers/story/story_controller.dart';
import 'empty_story_carousel_banner.dart';
import 'story_category_avatar.dart';

class StoryCarousel extends StatefulWidget {
  const StoryCarousel({super.key});

  @override
  StoryCarouselState createState() => StoryCarouselState();
}

class StoryCarouselState extends State<StoryCarousel> {
  final controller = Modular.get<OffersController>();
  final storyController = Modular.get<StoryController>();
  final baseController = Modular.get<BaseController>();
  final productDetailsController = Modular.get<ProductDetailsController>();

  int? loadingIndex;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      storyController.init();
    });
  }

  void _openStoryView(String categoriaName, Offset tapPosition) {
    final categoria = CategoriaMenu.getCategoriaByNome(categoriaName);
    final stories = storyController.storiesByCategory[categoriaName] ?? [];

    if (stories.isEmpty) {
      return;
    }

    final orderedCategories = storyController.storiesByCategory.keys.toList();

    int startIndex = storyController.getPosition(categoriaName);
    if (startIndex >= stories.length) {
      startIndex = stories.indexWhere((story) => !story.visualizado);
      startIndex = startIndex == -1 ? 0 : startIndex;
    }

    storyController.lastTapPosition(tapPosition);

    setState(() {
      loadingIndex = null;
    });

    Modular.to.pushNamed(
      '/offers/storyView',
      arguments: {
        'orderedCategories': orderedCategories,
        'initialCategory': categoriaName,
        'initialIndex': startIndex,
        'categoria': categoria,
        'onStoryComplete': (productId) {
          storyController.markStoryAsViewed(productId);
        },
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([storyController, controller]),
      builder: (context, _) {
        if (storyController.storiesByCategory.isEmpty) {
          return EmptyStoryCarouselBanner();
        }

        return Container(
          height: 128,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: storyController.storiesByCategory.length,
            itemBuilder: (context, index) {
              final entry = storyController.storiesByCategory.entries.elementAt(index);
              final String categoriaNome = entry.key;
              final List<StoryModel> stories = entry.value;
              final categoria = CategoriaMenu.getCategoriaByNome(categoriaNome);

              return GestureDetector(
                onTapUp: (TapUpDetails details) {
                  setState(() {
                    loadingIndex = index;
                  });
                  _openStoryView(categoriaNome, details.globalPosition);
                },
                child: StoryCategoryAvatar(
                  loadingIndex: loadingIndex,
                  categoria: categoria,
                  stories: stories,
                  categoriaNome: categoriaNome,
                  index: index,
                ),
              );
            },
          ),
        );
      },
    );
  }
}

Color getFilteredColor(Color categoria) {
  return Color.alphaBlend(Color.fromRGBO(255, 255, 255, 0.5), categoria);
}
