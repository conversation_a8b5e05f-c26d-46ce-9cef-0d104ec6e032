import 'package:flutter/material.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../core/base/widgets/no_connect.dart';
import '../../widgets/profile/header_with_back_button.dart';

class AboutPromobell extends StatelessWidget {
  const AboutPromobell({super.key});

  @override
  Widget build(BuildContext context) {
    return NoConnect(
      child: Scaffold(
        backgroundColor: ColorOutlet.paper,
        body: SelectionArea(
          child: Column(
            children: [
              HeaderWithBackButton(title: 'Sobre o Promobell'),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: <PERSON>umn(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: const [
                      SizedBox(height: 12),
                      _Section(
                        title: 'O Promobell é confiável?',
                        content:
                            'Sim! O Promobell atua apenas como um canal de divulgação de ofertas, direcionando os usuários para lojas consolidadas e confiáveis como Amazon, Mercado Livre e Magazine Luiza. Todas as transações são realizadas diretamente nos sites oficiais das lojas, garantindo segurança para o comprador.',
                      ),
                      Divider(),
                      _Section(
                        title: 'O Promobell vende produtos?',
                        content:
                            'Não. O Promobell atua exclusivamente como um canal de divulgação de ofertas, direcionando os usuários para sites de terceiros, como Amazon, Mercado Livre e Magazine Luiza. Não realizamos vendas, não intermediamos transações e não garantimos a entrega dos produtos.',
                      ),
                      Divider(),
                      _Section(
                        title:
                            'O Promobell escolhe os preços das ofertas?',
                        content:
                            'Não. Todos os preços são definidos pelas lojas anunciantes e podem mudar a qualquer momento. Nosso aplicativo apenas divulga as ofertas disponíveis, sem qualquer controle sobre os valores exibidos.',
                      ),
                      Divider(),
                      _Section(
                        title:
                            'O preço da oferta está diferente no site da loja. O que aconteceu?',
                        content:
                            'Os preços exibidos no Promobell são obtidos diretamente das lojas parceiras e podem sofrer alterações a qualquer momento, sem aviso prévio. Recomendamos sempre verificar o valor final no site do vendedor antes de concluir a compra.',
                      ),
                      Divider(),
                      _Section(
                        title:
                            'Como reportar uma oferta com informações erradas ou desatualizadas?',
                        content:
                            'Se encontrar uma oferta com preço desatualizado, produto esgotado ou qualquer outra divergência, você pode reportá-la diretamente dentro do próprio aplicativo. Nossa equipe analisará o caso e tomará as medidas necessárias para manter as informações sempre atualizadas.',
                      ),
                      Divider(),
                      _Section(
                        title:
                            'Comprei um produto divulgado no Promobell, mas ele não foi entregue. O que faço?',
                        content:
                            'Como não realizamos vendas, qualquer questão relacionada a entrega, pagamento ou suporte pós-venda deve ser tratada diretamente com a loja onde a compra foi realizada. Recomendamos verificar o status do pedido no site do vendedor e entrar em contato com o suporte da loja, se necessário.',
                      ),
                      Divider(),
                      _Section(
                        title:
                            'Por que o Promobell divulga apenas ofertas da Amazon, Mercado Livre e Magazine Luiza?',
                        content:
                            'Escolhemos trabalhar com Amazon, Mercado Livre e Magazine Luiza porque são plataformas de grande credibilidade e alcance nacional, oferecendo segurança na compra, variedade de produtos e boas políticas de atendimento ao cliente.\n\nAlém disso, essas varejistas disponibilizam as melhores ofertas do mercado, com maior potencial de descontos e cupons diariamente, além de uma logística eficiente, garantindo frete grátis em diversas compras e entregas rápidas, muitas vezes em regime full time.\n\nDessa forma, garantimos que nossos usuários tenham acesso a ofertas de lojas confiáveis e com alto volume de transações no mercado, proporcionando uma experiência de compra segura e vantajosa.',
                      ),
                      Divider(),
                      _Section(
                        title:
                            'Por que o Promobell não tem grupos no WhatsApp ou Telegram?',
                        content:
                            'Optamos por não utilizar grupos de WhatsApp ou Telegram para evitar o envio excessivo de mensagens e garantir uma experiência mais organizada e sem interrupções.\n\nValorizamos sua privacidade. Em grupos, os números dos participantes ficam visíveis para todos, o que pode comprometer a segurança dos seus dados. No Promobell, seguimos diretrizes rigorosas para garantir que suas informações não sejam expostas ou compartilhadas.\n\nPara acompanhar as melhores ofertas de forma segura, ative as notificações push no app ou siga nossos canais oficiais WhatsApp ou instagram.Promobell.',
                      ),
                      Divider(),
                      _Section(
                        title:
                            'Como entro em contato com o suporte do Promobell?',
                        content:
                            'Se precisar de suporte ou tiver dúvidas que não foram respondidas aqui, entre em contato pelo nosso canal oficial:\n\<EMAIL>',
                      ),
                      SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Widget auxiliar reaproveitável
class _Section extends StatelessWidget {
  final String title;
  final String content;

  const _Section({required this.title, required this.content});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(title),
        _buildFormattedText(content),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 16, bottom: 8),
      child: TextPattern.customText(
        text: title,
        fontSize: 20,
        fontWeightOption: FontWeightOption.bold,
        isSelectable: true,
      ),
    );
  }

  Widget _buildFormattedText(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: SelectableText(
        text,
        style: TextStyle(
          fontSize: 14,
          color: ColorOutlet.contentSecondary,
          fontFamily: TextPattern().fontFamily,
        ),
      ),
    );
  }
}

class Divider extends StatelessWidget {
  const Divider({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8, top: 16),
      child: Container(
        height: 1,
        width: double.infinity,
        color: ColorOutlet.contentSecondary.withValues(alpha: 0.1),
      ),
    );
  }
}
