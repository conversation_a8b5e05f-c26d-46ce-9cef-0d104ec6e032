import 'package:flutter/material.dart';

import '../../../../../../theme/color_outlet.dart';

class ThinSeparator extends StatelessWidget {
  final double bottomPadding;

  const ThinSeparator({this.bottomPadding = 0, super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: bottomPadding),
      child: Container(height: 1, width: double.infinity, color: ColorOutlet.contentSecondary.withValues(alpha: 0.1)),
    );
  }
}
