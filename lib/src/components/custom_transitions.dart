import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../modules/offers/controllers/story/story_controller.dart';

class CustomTransitions {
  static CustomTransition fromLeft() {
    return CustomTransition(
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(-1.0, 0.0); // Começa da esquerda
        const end = Offset.zero;
        var tweenEntrada = Tween(begin: begin, end: end).chain(CurveTween(curve: Curves.easeInOut));

        return SlideTransition(position: animation.drive(tweenEntrada), child: child);
      },
    );
  }

  static CustomTransition fromRight() {
    return CustomTransition(
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0); // Começa da direita
        const end = Offset.zero;
        var tweenEntrada = Tween(begin: begin, end: end).chain(CurveTween(curve: Curves.easeInOut));

        return SlideTransition(position: animation.drive(tweenEntrada), child: child);
      },
    );
  }

  static CustomTransition fadeIn() {
    return CustomTransition(
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: Tween<double>(
            begin: 0.5,
            end: 1.0,
          ).animate(CurvedAnimation(parent: animation, curve: Curves.easeIn)),
          child: child,
        );
      },
    );
  }

  static CustomTransition storyTransition() {
    return CustomTransition(
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        // Mudamos a lógica de detecção de reversão
        final isReverse = secondaryAnimation.status == AnimationStatus.forward;

        final storyController = Modular.get<StoryController>();

        final tapPosition = storyController.tapPosition as Offset?;

        final screenSize = MediaQuery.of(context).size;

        debugPrint('Story Transition - isReverse: $isReverse, tapPosition: $tapPosition');

        final relativeX = tapPosition?.dx ?? (screenSize.width / 2);
        final relativeY = tapPosition?.dy ?? (screenSize.height / 2);

        final xOffset = (relativeX / screenSize.width) - 0.5;
        final yOffset = (relativeY / screenSize.height) - 0.5;

        final slideAnimation = Tween<Offset>(
          begin: isReverse ? Offset.zero : Offset(xOffset, yOffset),
          end: isReverse ? Offset(xOffset, yOffset) : Offset.zero,
        ).animate(CurvedAnimation(parent: isReverse ? secondaryAnimation : animation, curve: Curves.easeOut));

        final scaleAnimation = Tween<double>(
          begin: isReverse ? 1.0 : 0.0,
          end: isReverse ? 0.0 : 1.0,
        ).animate(CurvedAnimation(parent: isReverse ? secondaryAnimation : animation, curve: Curves.easeOut));

        final fadeAnimation = Tween<double>(
          begin: isReverse ? 1.0 : 0.0,
          end: isReverse ? 0.0 : 1.0,
        ).animate(CurvedAnimation(parent: isReverse ? secondaryAnimation : animation, curve: Curves.easeOut));

        return Stack(
          children: [
            FadeTransition(
              opacity: fadeAnimation,
              child: SlideTransition(
                position: slideAnimation,
                child: ScaleTransition(scale: scaleAnimation, child: child),
              ),
            ),
          ],
        );
      },
    );
  }
}
