import 'package:flutter_modular/flutter_modular.dart';

import '../components/custom_transitions.dart';
import '../core/base/controllers/base_controller.dart/base_controller.dart';
import '../core/base/home_base.dart';
import '../core/guards/auth_guard.dart';
import '../core/splash_page.dart';
import '../modules/categories/categories_module.dart';
import '../modules/coupons/coupons_module.dart';
import '../modules/offers/controllers/notification_controller.dart';
import '../modules/offers/controllers/offers_controller.dart';
import '../modules/offers/controllers/products_details_controller.dart';
import '../modules/offers/controllers/story/story_animation_controller.dart';
import '../modules/offers/controllers/story/story_controller.dart';
import '../modules/offers/controllers/story/story_navigation_controller.dart';
import '../modules/offers/offers_module.dart';
import '../modules/profile/controllers/login_controller.dart';
import '../modules/profile/controllers/profile_controller.dart';
import '../modules/profile/profile_module.dart';
import '../services/deep_link_route_handler.dart';
import '../services/digital_ocean/config/digital_ocean_config.dart';
import '../services/digital_ocean/connection/i_storage_connection.dart';
import '../services/digital_ocean/connection/storage_connection.dart';
import '../services/navigation/scroll_services.dart';
import '../services/supabase/connection/connection.dart';
import '../services/supabase/connection/i_connection.dart';
import '../services/supabase/db/db.dart';
import '../services/supabase/produtos/get/get_produtos.dart';
import '../services/supabase/usuarios/get/get_usuarios.dart';
import '../services/supabase/usuarios/post/post_usuarios.dart';
import '../services/supabase/usuarios/put/put_usuarios.dart';

class AppModule extends Module {
  static const String splashRoute = '/';
  static const String homeRoute = '/home';
  static const String offersModule = '/offers';
  static const String couponsModule = '/coupons';
  static const String categoriesModule = '/categories';
  static const String profileModule = '/profile';

  // Rotas para deep links
  static const String productRoute = '/product';
  static const String categoryRoute = '/category';

  @override
  void binds(i) {
    // Core
    i.addSingleton<IConnection>(Connection.new);
    i.addSingleton<DB>(DB.new);
    i.addSingleton<IStorageConnection>(() => StorageConnection(config: DigitalOceanConfig.fromEnv()));

    // Services
    i.addSingleton(() => GetUsuarios(connection: i.get<IConnection>(), db: i.get<DB>()));
    i.addSingleton(() => PostUsuarios(connection: i.get<IConnection>(), db: i.get<DB>()));
    i.addSingleton(() => PutUsuarios(connection: i.get<IConnection>(), db: i.get<DB>()));
    i.addSingleton(() => GetProdutos(connection: i.get<IConnection>(), db: i.get<DB>()));
    i.addSingleton(ScrollService.new);

    // Controllers
    i.addSingleton(() => BaseController(putUsuarios: i.get<PutUsuarios>(), scrollService: i.get<ScrollService>()));
    i.addSingleton(NotificationController.new);
    i.addSingleton(() => LoginController(postUsuarios: i.get<PostUsuarios>(), getUsuarios: i.get<GetUsuarios>()));
    i.addSingleton(
      () => ProfileController(
        getUsuarios: i.get<GetUsuarios>(),
        storageConnection: i.get<IStorageConnection>(),
        putUsuarios: i.get<PutUsuarios>(),
      ),
    );
    i.addSingleton(() => OffersController(getProdutos: i.get<GetProdutos>()));
    i.addSingleton(() => StoryController());
    i.add<ProductDetailsController>(ProductDetailsController.new);
    i.add<StoryAnimationController>(StoryAnimationController.new);
    i.add<StoryNavigationController>(StoryNavigationController.new);
  }

  @override
  void routes(RouteManager r) {
    r.child(splashRoute, child: (_) => const SplashPage());
    r.child(
      homeRoute,
      child: (_) => const HomeBasePage(),
      guards: [AuthGuard()],
      transition: TransitionType.custom,
      customTransition: CustomTransition(
        transitionBuilder: (context, animation, secondaryAnimation, child) {
          final data = r.args.data;
          final isTransitionPrimary = (data is Map ? data['isTransitionPrimary'] : false) ?? false;

          if (isTransitionPrimary) {
            return CustomTransitions.fromLeft().transitionBuilder(context, animation, secondaryAnimation, child);
          } else {
            return CustomTransitions.fadeIn().transitionBuilder(context, animation, secondaryAnimation, child);
          }
        },
      ),
      duration: const Duration(milliseconds: 200),
    );
    r.module(
      offersModule,
      module: OffersModule(),
      guards: [AuthGuard()],
      transition: TransitionType.scale,
      duration: const Duration(milliseconds: 200),
    );
    r.module(
      couponsModule,
      module: CouponsModule(),
      guards: [AuthGuard()],
      transition: TransitionType.scale,
      duration: const Duration(milliseconds: 200),
    );
    r.module(
      categoriesModule,
      module: CategoriesModule(),
      guards: [AuthGuard()],
      transition: TransitionType.scale,
      duration: const Duration(milliseconds: 200),
    );
    r.module(profileModule, module: ProfileModule());

    // Rotas para deep links
    r.child(
      productRoute,
      child: (context) => DeepLinkRouteHandler(routeType: 'product', params: r.args.data),
      guards: [AuthGuard()],
      transition: TransitionType.fadeIn,
      duration: const Duration(milliseconds: 100),
    );
    r.child(
      categoryRoute,
      child: (context) => DeepLinkRouteHandler(routeType: 'category', params: r.args.data),
      guards: [AuthGuard()],
      transition: TransitionType.fadeIn,
      duration: const Duration(milliseconds: 100),
    );
  }
}
