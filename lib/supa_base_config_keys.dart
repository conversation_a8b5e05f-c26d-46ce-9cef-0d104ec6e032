class SupaBaseConfigKeys {
  final String apiUrl;
  final String apiAnonKey;
  final String apiServiceRoles;

  SupaBaseConfigKeys({required this.apiUrl, required this.apiAnonKey, required this.apiServiceRoles});

  factory SupaBaseConfigKeys.fromMap(Map<String, dynamic> map) {
    return SupaBaseConfigKeys(apiUrl: map['API_URL'], apiAnonKey: map['API_ANON_KEY'], apiServiceRoles: map['API_SERVICE_ROLES']);
  }

  Map<String, dynamic> toMap() {
    return {'API_URL': apiUrl, 'API_ANON_KEY': apiAnonKey, 'API_SERVICE_ROLES': apiServiceRoles};
  }
}
