import '../../../../models/user_profile_model.dart';
import '../../../exceptions/app_exception.dart';
import '../../../logs/app_logger.dart';
import '../../connection/i_connection.dart';
import '../../db/db.dart';

class PutUsuarios {
  final IConnection _connection;
  final DB _db;

  PutUsuarios({required IConnection connection, required DB db})
    : _connection = connection,
      _db = db;

  Future<UserProfileModel> updateUser(UserProfileModel user) async {
    try {
      final data = await _connection.put(
        table: _db.tabelaDeUsuarios,
        body: {
          'name': user.name,
          'surname': user.surname,
          'image': user.image,
          'image_id': user.imageId,
          'token_firebase': user.tokenFirebase,
          'birth_date': user.birthDate?.toIso8601String(),
          'gender': user.gender,
        },
        filter: {'email': user.email},
      );

      if (data.isEmpty) {
        throw DatabaseException('Falha ao atualizar usuário');
      }

      return UserProfileModel.fromMap(data.first);
    } catch (e, stack) {
      AppLogger.logError('Erro ao atualizar usuário', e, stack);
      rethrow;
    }
  }

  Future<UserProfileModel> updateDataUser({
    required String email,
    required String name,
    required String surname,
    required int gender,
    required DateTime birthDate,
  }) async {
    try {
      final data = await _connection.put(
        table: _db.tabelaDeUsuarios,
        body: {
          'name': name,
          'surname': surname,
          'gender': gender,
          'birth_date': birthDate.toIso8601String(),
        },
        filter: {'email': email},
      );

      if (data.isEmpty) {
        throw DatabaseException('Falha ao atualizar usuário');
      }

      return UserProfileModel.fromMap(data.first);
    } catch (e, stack) {
      AppLogger.logError('Erro ao atualizar usuário', e, stack);
      rethrow;
    }
  }

  Future<void> updateUserImageMapping(
    String email,
    String imageId,
  ) async {
    try {
      await _connection.put(
        table: _db.tabelaDeUsuarios,
        body: {'image_id': imageId},
        filter: {'email': email},
      );
    } catch (e, stack) {
      AppLogger.logError(
        'Erro ao atualizar mapeamento de imagem',
        e,
        stack,
      );
      rethrow;
    }
  }

  Future<void> postTokenFCM({
    required String token,
    required String email,
  }) async {
    try {
      await _connection.put(
        table: _db.tabelaDeUsuarios,
        body: {'token_firebase': token},
        filter: {'email': email},
      );
    } catch (e, stack) {
      AppLogger.logError('Error no postTokenFCM:', e, stack);
      rethrow;
    }
  }
}
