import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../../theme/color_outlet.dart';

import '../../../../offers/controllers/products_details_controller.dart';
import '../../../controllers/categories_controller.dart';
import '../../pages/details_category_page.dart';

class CategoryBackground extends StatelessWidget {
  final CategoriesController controller;

  final DetailsCategoryPage widget;

  const CategoryBackground({super.key, required this.controller, required this.widget});

  @override
  Widget build(BuildContext context) {
    return Container(height: 1.sh, color: controller.getFilteredColor(widget.category));
  }
}

class CategoryBackgroundAnimated extends StatelessWidget {
  final CategoriesController? controller;
  final ProductDetailsController? scrollController;
  final bool? isDetailsProduct;

  const CategoryBackgroundAnimated({super.key, this.controller, this.scrollController, this.isDetailsProduct = false});

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 100),
      transform:
          isDetailsProduct!
              ? Matrix4.translationValues(0, scrollController!.backgroundTranslateY, 0)
              : Matrix4.translationValues(0, controller!.moveBackgroundDown, 0),
      child: Container(height: 1.sh, color: ColorOutlet.surface),
    );
  }
}
