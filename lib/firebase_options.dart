// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart' show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError('DefaultFirebaseOptions are not supported for this platform.');
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyA05NT-UDxKcW2RjzPHp_paSyANfXkeQYI',
    appId: '1:357298624318:web:8b4ad36c605dc80915fe6f',
    messagingSenderId: '357298624318',
    projectId: 'chegou-mercado-cliente',
    authDomain: 'chegou-mercado-cliente.firebaseapp.com',
    storageBucket: 'chegou-mercado-cliente.appspot.com',
    measurementId: 'G-XK2GYB756D',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD1-0k-e8lLHU-icCPzCQkDby2Yaz8W1Ro',
    appId: '1:667843897010:android:8909defe5759a962808bab',
    messagingSenderId: '667843897010',
    projectId: 'promobell-dev',
    storageBucket: 'promobell-dev.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDy2r256bxb8OozrcyNdGzCYlSV6JWQHAU',
    appId: '1:667843897010:ios:adb467f2b5b01baf808bab',
    messagingSenderId: '667843897010',
    projectId: 'promobell-dev',
    storageBucket: 'promobell-dev.firebasestorage.app',
    iosBundleId: 'br.com.promobell.promobell',
  );
}
