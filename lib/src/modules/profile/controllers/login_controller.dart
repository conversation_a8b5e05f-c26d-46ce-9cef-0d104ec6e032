import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:timezone/timezone.dart' as tz;

import '../../../../app_links_service.dart';
import '../../../../firebase_messaging_service.dart';
import '../../../../theme/svg_icons.dart';
import '../../../components/custom_snack_bar.dart';
import '../../../core/base/controllers/base_controller.dart/base_controller.dart';
import '../../../models/user_delete_profile.dart';
import '../../../models/user_profile_model.dart';
import '../../../services/logs/app_logger.dart';
import '../../../services/supabase/delete_account/delete_account_db.dart';
import '../../../services/supabase/usuarios/get/get_usuarios.dart';
import '../../../services/supabase/usuarios/post/post_usuarios.dart';
import 'profile_controller.dart';

class LoginController with ChangeNotifier {
  final PostUsuarios _postUsuarios;
  final GetUsuarios _getUsuarios;

  LoginController({
    required PostUsuarios postUsuarios,
    required GetUsuarios getUsuarios,
  }) : _postUsuarios = postUsuarios,
       _getUsuarios = getUsuarios;

  UserProfileModel? userProfile = UserProfileModel.empty();
  final supabase = Supabase.instance.client;
  late StreamSubscription<AuthState> _authSubscription;
  Future<tz.TZDateTime> getBrazilDateTime() async {
    final saoPauloTimeZone = tz.getLocation('America/Sao_Paulo');
    final now = tz.TZDateTime.now(saoPauloTimeZone);
    return now;
  }

  final FirebaseMessagingService firebaseMessagingService =
      FirebaseMessagingService();
  void initAuthListener() {
    _authSubscription = supabase.auth.onAuthStateChange.listen((
      data,
    ) async {
      final event = data.event;
      if (event == AuthChangeEvent.signedIn) {
        final user = data.session?.user;
        final provider = user?.appMetadata['provider'] as String?;

        final prefs = await SharedPreferences.getInstance();
        final appleFullName = prefs.getString('appleFullName');

        // Token FCM
        String token =
            await firebaseMessagingService.getToken() ?? '';

        // 🔹 Define nome: preferencialmente do Apple SignIn (salvo antes)
        final nameFromApple =
            (provider == 'apple' &&
                    appleFullName != null &&
                    appleFullName.isNotEmpty)
                ? appleFullName
                : user?.userMetadata?['full_name'] ?? '';

        userProfile = UserProfileModel(
          name: nameFromApple.trim(),
          email: user?.email,
          image: user?.userMetadata?['avatar_url'],
          accessToken: data.session?.accessToken,
          joinedDate: DateTime.now(),
          tokenFirebase: token,
        );

        try {
          final existingUser = await _getUsuarios.getUserByEmail(
            userProfile!.email!,
          );

          if (existingUser != null) {
            userProfile = existingUser;
            AppLogger.logInfo('Usuário existente encontrado');
          } else {
            AppLogger.logInfo('Criando novo usuário');
            await _postUsuarios.createUser(userProfile!);
            final userData = await _getUsuarios.getUserByEmail(
              userProfile!.email!,
            );
            userProfile = userData;
          }

          await prefs.remove('appleFullName');

          await _handlePostSignIn(provider ?? 'google');
        } catch (e) {
          AppLogger.logError(
            'Erro ao processar usuário',
            e,
            StackTrace.current,
          );
        }
      }
    });
  }

  Future<void> _handlePostSignIn(String provider) async {
    if (userProfile?.accessToken != null &&
        userProfile?.email != null) {
      await saveUserData(userProfile!);
      await saveAccessToken(userProfile!.accessToken!, provider);

      final prefs = await SharedPreferences.getInstance();

      // 🔹 Nome + sobrenome vindos do login
      final fullName = userProfile?.name?.trim() ?? '';
      final nameParts = fullName.split(' ');
      final firstName = nameParts.isNotEmpty ? nameParts.first : '';
      final surname =
          nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

      // 🔹 Envia para o ProfileController
      final profileCtrl = Modular.get<ProfileController>();
      profileCtrl.setUserName(firstName, surname);

      // 🔹 Salva localmente também (SharedPreferences)
      await prefs.setString('userName', firstName);
      await prefs.setString('userSurname', surname);

      // 🔹 Gênero e nascimento: tenta obter do banco (userProfile)
      String? gender = prefs.getString('gender');
      String? birthDate = prefs.getString('birthDate');

      if (gender == null || birthDate == null) {
        final fetchedGender = userProfile?.gender?.toString();
        final fetchedBirthDate =
            userProfile?.birthDate != null
                ? DateFormat(
                  'dd/MM/yyyy',
                ).format(userProfile!.birthDate!)
                : null;

        if (fetchedGender != null) {
          gender = fetchedGender;
          await prefs.setString('gender', gender);
        }

        if (fetchedBirthDate != null) {
          birthDate = fetchedBirthDate;
          await prefs.setString('birthDate', birthDate);
        }
      }

      // 🔹 Decide próxima rota
      if (gender == null || birthDate == null) {
        Modular.to.navigate('/profile/onboarding');
      } else {
        Modular.to.navigate('/home');
      }

      AppLinksService.hasReceivedLink = false;
    } else {
      AppLogger.logError(
        'Token de acesso não disponível',
        null,
        StackTrace.current,
      );
    }
  }

  @override
  void dispose() {
    _authSubscription.cancel();
    super.dispose();
  }

  Future<AuthResponse> login() async {
    try {
      final webClientId = const String.fromEnvironment(
        'GOOGLE_WEB_CLIENT_ID',
      );
      final androidClientId = const String.fromEnvironment(
        'GOOGLE_ANDROID_CLIENT_ID',
      );
      final iosClientId = const String.fromEnvironment(
        'GOOGLE_IOS_CLIENT_ID',
      );
      final user = supabase.auth.currentUser;
      final GoogleSignIn googleSignIn = GoogleSignIn(
        clientId:
            user?.userMetadata?['provider'] == 'apple'
                ? iosClientId
                : androidClientId,
        serverClientId: webClientId,
      );
      final googleUser = await googleSignIn.signIn();
      final googleAuth = await googleUser!.authentication;
      final accessToken = googleAuth.accessToken;
      final idToken = googleAuth.idToken;

      if (accessToken == null) {
        throw 'Nenhum Access Token encontrado.';
      }
      if (idToken == null) {
        throw 'Nenhum ID Token encontrado.';
      }

      userProfile = UserProfileModel(
        name: user?.userMetadata?['full_name'],
        email: user?.email,
        image: user?.userMetadata?['avatar_url'],
        accessToken: user?.userMetadata?['access_token'],
      );

      notifyListeners();

      return supabase.auth.signInWithIdToken(
        provider: OAuthProvider.google,
        idToken: idToken,
        accessToken: accessToken,
      );
    } catch (e) {
      debugPrint('Login erro: $e');
      rethrow;
    }
  }

  bool isLoading = false;
  bool isLoadingSignOut = false;
  Future<void> handleGoogleSignIn(BuildContext context) async {
    isLoading = true;
    notifyListeners();

    try {
      await login().then((value) {
        if (context.mounted) {
          CustomSnackBar.show(
            context: context,
            message: "Login realizado com sucesso",
            icon: SvgIcons.feedbackCheck,
          );
        }
      });
    } catch (e) {
      debugPrint('Erro ao realizar login: $e');
      if (context.mounted) {
        CustomSnackBar.show(
          context: context,
          message: "Erro ao realizar login",
          icon: SvgIcons.feedbackInfo,
        );
      }
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  final baseController = Modular.get<BaseController>();
  final profileController = Modular.get<ProfileController>();
  Future<void> logOut() async {
    await signOut();
    Modular.to.pushReplacementNamed('/profile/login');
    baseController.index = 0;
    notifyListeners();
  }

  Future<void> signOut() async {
    try {
      isLoadingSignOut = true;
      notifyListeners();
      final GoogleSignIn googleSignIn = GoogleSignIn();
      await googleSignIn.signOut();
      await supabase.auth.signOut();
      await clearAccessToken();
      await clearUserData();
      userProfile = UserProfileModel.empty();
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      notifyListeners();
    } catch (e) {
      debugPrint('Erro ao realizar logout: $e');
      rethrow;
    } finally {
      isLoadingSignOut = false;
      notifyListeners();
    }
  }

  bool isLoadingApple = false;
  Future<void> handleAppleSignIn(BuildContext context) async {
    if (!Platform.isIOS && !Platform.isMacOS) {
      if (context.mounted) {
        CustomSnackBar.show(
          context: context,
          message:
              "Para fazer login com Apple é necessário estar utilizando um dispositivo IOS.",
          icon: SvgIcons.feedbackInfo,
        );
      }
      return;
    }

    isLoadingApple = true;
    notifyListeners();

    try {
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      if (credential.identityToken == null) {
        throw 'Nenhum Identity Token encontrado.';
      }

      // 🔹 Nome completo manualmente resgatado
      final fullName =
          credential.givenName != null
              ? '${credential.givenName} ${credential.familyName ?? ''}'
                  .trim()
              : null;

      // 🔹 Armazenar temporariamente para usar depois
      SharedPreferences prefs = await SharedPreferences.getInstance();
      if (fullName != null && fullName.isNotEmpty) {
        await prefs.setString('appleFullName', fullName);
      }
      prefs.setString('userEmail', credential.email ?? '');

      await supabase.auth.signInWithIdToken(
        provider: OAuthProvider.apple,
        idToken: credential.identityToken!,
      );
    } catch (e) {
      debugPrint('Erro ao realizar login: $e');
      if (context.mounted) {
        CustomSnackBar.show(
          context: context,
          message: "Erro ao realizar login com Apple",
          icon: SvgIcons.feedbackInfo,
        );
      }
    } finally {
      isLoadingApple = false;
      notifyListeners();
    }
  }

  Future<void> saveUserData(UserProfileModel user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('userName', user.name ?? '');
    await prefs.setString('userSurname', user.surname ?? '');
    await prefs.setString('userEmail', user.email ?? '');
    await prefs.setString('userImage', user.image ?? '');
    await prefs.setString('userPhone', user.phone ?? '');
    await prefs.setString(
      'userJoinedDate',
      user.joinedDate?.toIso8601String() ?? '',
    );
    // Só salva gender e birthDate se não forem null
    if (user.gender != null) {
      await prefs.setString('gender', user.gender.toString());
    }
    if (user.birthDate != null) {
      await prefs.setString(
        'birthDate',
        user.birthDate!.toIso8601String(),
      );
    }
  }

  Future<void> clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('userName');
    await prefs.remove('userSurname');
    await prefs.remove('userEmail');
    await prefs.remove('userImage');
    await prefs.remove('userPhone');
    await prefs.remove('userJoinedDate');
    await prefs.remove('gender');
    await prefs.remove('birthDate');
    await prefs.clear();

    AppLogger.logInfo('Dados do usuário limpos.');
  }

  Future<void> saveAccessToken(
    String accessToken,
    String provider,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('accessToken', accessToken);
    await prefs.setString('authProvider', provider);
    debugPrint('AccessToken salvo para $provider');
  }

  Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('accessToken');
  }

  Future<String?> getAuthProvider() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('authProvider');
  }

  Future<void> clearAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('accessToken');
    await prefs.remove('authProvider');
    AppLogger.logInfo('AccessToken e authProvider limpos.');
  }

  // Delete account

  String? selectedReason;
  TextEditingController optionalReasonController =
      TextEditingController();
  TextEditingController confirmController = TextEditingController();

  /// Atualiza o motivo selecionado
  void updateSelectedReason(String? reason) {
    selectedReason = reason;
    notifyListeners();
  }

  /// Atualiza o texto digitado no campo "EXCLUIR"
  void updateConfirmText(String value) {
    confirmController.text = value;
    notifyListeners();
  }

  // Atualiza o texto digitado no campo "Outro motivo"

  void updateOptionalReason(String value) {
    optionalReasonController.text = value;
    notifyListeners();
  }

  /// Validação para ativar/desativar o botão "Excluir conta"
  bool get isDeleteEnabled {
    final reasonSelected = selectedReason != null;
    // final isOtherReasonValid = selectedReason != 'Outro motivo' || (optionalReasonController.text.trim().isNotEmpty);
    final isConfirmValid = confirmController.text.trim() == 'EXCLUIR';
    return reasonSelected && isConfirmValid;
  }

  DeleteAccountDB deleteUserDb = DeleteAccountDB();
  bool isLoadingDeleteAccount = false;

  Future<bool> deleteAccount() async {
    isLoadingDeleteAccount = true;
    notifyListeners();
    try {
      final userProfile = await getUserProfile();
      await deleteUserDb.saveDeletedUser(user: userProfile);

      final deleteSuccess = await deleteUserDb.deleteUser(
        email: userProfile.email!,
      );
      if (!deleteSuccess) {
        AppLogger.logError(
          'Falha ao deletar usuário do banco de dados',
          'Email: ${userProfile.email}',
          StackTrace.current,
        );
        throw Exception('Falha ao deletar usuário do banco de dados');
      }

      resetDeleteAccount();
      await clearUserData();
      await clearAccessToken();
      profileController.resetProfileState();
      await signOut();
      baseController.index = 0;
      Modular.to.pushNamedAndRemoveUntil('/home', (route) => false);
      isLoadingDeleteAccount = false;
      notifyListeners();
      return true;
    } catch (e) {
      isLoadingDeleteAccount = false;
      return false;
    }
  }

  void resetDeleteAccount() {
    selectedReason = null;
    optionalReasonController.clear();
    confirmController.clear();
  }

  Future<UserDeleteProfile> getUserProfile() async {
    final UserProfileModel? user = await _getUsuarios.getUserByEmail(
      Supabase.instance.client.auth.currentUser?.email ?? '',
    );

    UserDeleteProfile userProfileDelete = UserDeleteProfile(
      name: user?.name,
      email: user?.email,
      phone: user?.phone,
      image: user?.image,
      joinedDate: user?.joinedDate,
      accessToken: user?.accessToken,
      imageId: user?.imageId,
      tokenFirebase: user?.tokenFirebase,
      razonDelete: selectedReason,
      optionalReason: optionalReasonController.text,
    );
    return userProfileDelete;
  }
}
