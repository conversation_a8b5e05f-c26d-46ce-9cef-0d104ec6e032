import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/custom_pop_scope.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../core/base/widgets/no_connect.dart';
import '../../widgets/profile/header_with_back_button.dart';
import '../../widgets/profile/thin_separator.dart';

class PrivacyPolitics extends StatefulWidget {
  const PrivacyPolitics({super.key});

  @override
  State<PrivacyPolitics> createState() => _PrivacyPoliticsState();
}

class _PrivacyPoliticsState extends State<PrivacyPolitics> {
  final List<String> boldWords = ["Promobell", "Amazon", "Mercado Livre", "Magazine Luiza"];
  final RegExp emailRegex = RegExp(r'\b[\w\.-]+@[\w\.-]+\.\w+\b');
  @override
  Widget build(BuildContext context) {
    return CustomPopScope(
      index: 2,
      child: NoConnect(
        child: Scaffold(
          backgroundColor: ColorOutlet.paper,
          body: SelectionArea(
            child: Column(
              children: [
                HeaderWithBackButton(title: 'Política de Privacidade'),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSectionTitle('Introdução'),
                          _buildFormattedText(
                            'A transparência e a segurança no tratamento dos dados pessoais dos usuários do Promobell são compromissos fundamentais da nossa empresa. Esta Política de Privacidade estabelece as regras sobre a coleta, uso, armazenamento e proteção das informações dos usuários, garantindo conformidade com a legislação aplicável.',
                          ),
                          _buildFormattedText(
                            'O Promobell atua exclusivamente como um canal de divulgação de ofertas de terceiros, incluindo Amazon, Mercado Livre e Magazine Luiza, das quais somos afiliados. O aplicativo não realiza vendas, não intermedia transações e não garante a entrega ou o recebimento de produtos adquiridos pelos usuários por meio dos links promocionais divulgados.',
                          ),
                          _buildFormattedText(
                            'Além disso, adotamos medidas rigorosas para proteger os dados pessoais dos usuários e assegurar que seu uso seja feito de forma ética e responsável, em conformidade com a Lei Geral de Proteção de Dados (Lei nº 13.709/2018 – LGPD).',
                          ),
                          _buildFormattedText(
                            'Assim, é essencial que os usuários compreendam como suas informações são tratadas e utilizem o serviço de acordo com as disposições aqui estabelecidas.',
                          ),
                          _buildFormattedText(
                            'Ao acessar ou utilizar o Promobell, o usuário declara estar ciente e concorda com as condições descritas neste documento. Caso não concorde com qualquer cláusula desta Política de Privacidade, recomendamos que interrompa o uso do serviço.',
                          ),
                          ThinSeparator(),
                          _buildSectionTitle('1. Coleta de Dados'),
                          _buildFormattedText(
                            'Para garantir maior segurança e confiabilidade, o Promobell adota o cadastro via Google ou Apple, permitindo que o usuário tenha controle sobre sua conta sem a necessidade de criar novas credenciais dentro do aplicativo.',
                          ),
                          _buildFormattedText('Os dados coletados incluem:'),
                          _buildBulletList([
                            'E-mail;',
                            'Nome;',
                            'Sobrenome*;',
                            'Data de nascimento;',
                            'Gênero*;',
                            'Nome e foto de perfil (opcionais, inseridos pelo usuário).',
                          ]),
                          _buildFormattedText(
                            '*Campos opcionais, inseridos voluntariamente pelo usuário durante ou após o processo de cadastro.',
                          ),
                          _buildFormattedText(
                            'Eventualmente, podemos coletar dados adicionais fornecidos voluntariamente pelo usuário no decorrer da sua experiência dentro do aplicativo.',
                          ),
                          ThinSeparator(),
                          _buildSectionTitle('2. Finalidade do Uso dos Dados'),
                          _buildFormattedText('Os dados coletados são utilizados exclusivamente para os seguintes fins:'),
                          _buildBulletList([
                            'Garantir a veracidade dos usuários e evitar fraudes;',
                            'Personalizar a experiência do usuário, oferecendo ofertas e recomendações mais relevantes;',
                            'Realizar análises de comportamento, ajudando na melhoria contínua do serviço;',
                            'Fornecer suporte e atendimento, garantindo uma experiência mais eficiente;',
                            'Cumprir obrigações legais e regulatórias, quando necessário.',
                          ]),
                          _buildFormattedText(
                            'O Promobell NÃO compartilha, vende ou cede os dados pessoais dos usuários para terceiros, parceiros ou anunciantes, exceto quando exigido por lei ou determinação judicial.',
                          ),
                          ThinSeparator(),
                          _buildSectionTitle('3. Armazenamento e Segurança dos Dados'),
                          _buildFormattedText(
                            'Os dados dos usuários são armazenados de maneira segura durante todo o período em que sua conta estiver ativa no aplicativo. Caso o usuário solicite a exclusão da conta, seus dados serão removidos, exceto aqueles necessários para o cumprimento de obrigações legais.',
                          ),
                          _buildFormattedText(
                            'Adotamos medidas de proteção para evitar acessos não autorizados, bem como monitoramento contínuo para prevenir fraudes e garantir a integridade dos dados armazenados.',
                          ),
                          ThinSeparator(),
                          _buildSectionTitle('4. Controle, Gerenciamento e Retenção dos Dados'),
                          _buildFormattedText('Os usuários têm total autonomia sobre seus dados e podem:'),
                          _buildFormattedText(
                            'Alterar ou atualizar informações diretamente no aplicativo, acessando a aba "Editar Perfil";\nExcluir sua conta a qualquer momento, sem burocracia, confirmando a exclusão dentro do próprio aplicativo.',
                          ),
                          _buildFormattedText(
                            'Mesmo após a exclusão da conta, algumas informações anonimizadas (como registros de interações e curtidas) podem ser mantidas para fins estatísticos e analíticos, conforme previsto no tópico 3.',
                          ),
                          ThinSeparator(),
                          _buildSectionTitle('5. Cookies e Tecnologias de Rastreamento'),
                          _buildFormattedText(
                            'O Promobell utiliza cookies e tecnologias de rastreamento para aprimorar a experiência do usuário e otimizar nossos serviços. Esses dados permitem:',
                          ),
                          _buildBulletList([
                            'Melhoria na usabilidade e navegação do aplicativo;',
                            'Personalização de conteúdo e recomendações com base nas preferências do usuário;',
                          ]),
                          _buildFormattedText('Análises estatísticas e de comportamento, visando a melhoria contínua do app.'),
                          ThinSeparator(),
                          _buildSectionTitle('6. Alterações na Política de Privacidade'),
                          _buildFormattedText(
                            'O Promobell se reserva o direito de atualizar este documento periodicamente para atender exigências legais, regulatórias ou operacionais, garantindo o aprimoramento contínuo da proteção de dados e da privacidade dos usuários.',
                          ),
                          _buildFormattedText(
                            'Sempre que houver alterações significativas, os usuários serão notificados via push notification e dentro do próprio aplicativo.',
                          ),
                          _buildFormattedText(
                            'Recomendamos que os usuários revisem periodicamente esta Política de Privacidade para se manterem informados sobre eventuais atualizações.',
                          ),
                          ThinSeparator(),
                          _buildSectionTitle('7. Vigência e Disposições Finais'),
                          _buildFormattedText(
                            'A utilização do Promobell implica na leitura, compreensão e concordância com esta Política de Privacidade e suas diretrizes.',
                          ),
                          _buildFormattedText(
                            'Se o usuário tiver qualquer dúvida sobre esta Política de Privacidade, desejar exercer seus direitos de acesso, retificação ou exclusão de dados, poderá entrar em contato através do seguinte canal oficial:',
                          ),
                          _buildFormattedText('<EMAIL>'),
                          _buildFormattedText(
                            'Nos comprometemos a responder às solicitações dentro dos prazos estabelecidos pela legislação vigente.',
                          ),
                          _buildFormattedText(
                            'Esta Política de Privacidade entra em vigor na data de sua publicação e permanecerá válida até que uma nova versão seja disponibilizada.',
                          ),
                          ThinSeparator(bottomPadding: 24),
                          TextPattern.customText(text: '© Promobell LTDA'),
                          const SizedBox(height: 32),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 24, bottom: 24),
      child: TextPattern.customText(text: title, fontSize: 20, fontWeightOption: FontWeightOption.bold, isSelectable: true),
    );
  }

  Widget _buildFormattedText(String text, {double bottomPadding = 24}) {
    return Padding(
      padding: EdgeInsets.only(bottom: bottomPadding),
      child: SelectableText.rich(
        TextSpan(
          children: _buildTextSpans(text),
          style: TextStyle(fontSize: 14, color: ColorOutlet.contentSecondary, fontFamily: TextPattern().fontFamily),
        ),
      ),
    );
  }

  Widget _buildBulletList(List<String> items) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: items.map((item) => _buildFormattedText('• $item', bottomPadding: 0)).toList(),
      ),
    );
  }

  List<TextSpan> _buildTextSpans(String text) {
    List<TextSpan> spans = [];
    text.splitMapJoin(
      RegExp("${boldWords.map((word) => "\\b${RegExp.escape(word)}\\b").join('|')}|${emailRegex.pattern}"),
      onMatch: (match) {
        String matchedText = match.group(0) ?? "";
        if (emailRegex.hasMatch(matchedText)) {
          spans.add(
            TextSpan(
              text: matchedText,
              style: TextStyle(
                color: ColorOutlet.contentPrimary,
                fontWeight: FontWeight.w400,
                decoration: TextDecoration.underline,
                fontFamily: TextPattern().fontFamily,
              ),
              recognizer: TapGestureRecognizer()..onTap = () => launchUrl(Uri.parse("mailto:$matchedText")),
            ),
          );
        } else {
          spans.add(TextSpan(text: matchedText, style: TextStyle(fontWeight: FontWeight.bold)));
        }
        return '';
      },
      onNonMatch: (nonMatch) {
        spans.add(TextSpan(text: nonMatch, style: TextStyle(fontWeight: FontWeight.normal)));
        return '';
      },
    );
    return spans;
  }
}
