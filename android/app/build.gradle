plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id 'com.google.gms.google-services' // Firebase
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode', '1').toInteger()
def flutterVersionName = localProperties.getProperty('flutter.versionName', '1.0.0')

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "br.com.promobell.promobell"
    compileSdk = 35
    ndkVersion = flutter.ndkVersion

    flavorDimensions "default"

    productFlavors {
        dev {
            dimension "default"
        }
        prod {
            dimension "default"
        }
    }

    defaultConfig {
        applicationId = "br.com.promobell.promobell"
        minSdkVersion 23
        targetSdkVersion 35
        versionCode = flutterVersionCode
        versionName = flutterVersionName
        multiDexEnabled true
        manifestPlaceholders += [
            'appAuthRedirectScheme': applicationId
        ]
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            // Enables code-related app optimization.
            minifyEnabled true

            // Enables resource shrinking.
            shrinkResources true

            // Default file with automatically generated optimization rules.
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt')
            signingConfig = signingConfigs.getByName("release")
            ndk {
                debugSymbolLevel 'SYMBOL_TABLE'
            }
        }
    }

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
}

flutter {
    source = "../.."
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.2'
    implementation 'com.google.android.gms:play-services-location:21.3.0'
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.android.support:appcompat-v7:28.0.0'
}

// ✅ Copia o google-services certo com base no flavor
gradle.taskGraph.whenReady { taskGraph ->
    def flavor = taskGraph.allTasks.any { it.name.toLowerCase().contains("dev") } ? "dev" : "prod"
    def sourceFile = file("google-services-${flavor}.json")
    def destinationFile = file("google-services.json")

    if (sourceFile.exists()) {
        destinationFile.text = sourceFile.text
        println "✅ Copiado google-services-${flavor}.json para google-services.json"
    } else {
        println "❌ Arquivo google-services-${flavor}.json não encontrado!"
    }
}