import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'logs/app_logger.dart';

class DeferredLinkService {
  static const String _isFirstLaunchKey = 'is_app_first_launch_v1';
  static const String _deviceIdKey = 'device_unique_id_v1';
  static const String _tabelaDeferredLinks = 'deferred_links';

  // final AppLinks _appLinks = AppLinks();
  final SupabaseClient _supabase = Supabase.instance.client;

  // Singleton pattern
  static final DeferredLinkService _instance =
      DeferredLinkService._internal();

  factory DeferredLinkService() {
    return _instance;
  }

  DeferredLinkService._internal();

  /// Método para inicializar o serviço. Deve ser chamado no início da aplicação.
  Future<void> initialize() async {
    try {
      await _setupDeviceId();
      // await _listenToDeepLinks(); // Comentado para desabilitar o listener de deep links gerais
      AppLogger.logInfo(
        'DeferredLinkService inicializado com sucesso (listener de deep links gerais desabilitado)',
      );
    } catch (e, stackTrace) {
      AppLogger.logError(
        'Erro ao inicializar DeferredLinkService',
        e,
        stackTrace,
      );
    }
  }

  /// Verifica se é primeira execução e processa links diferidos pendentes
  Future<Map<String, dynamic>?>
  checkDeferredLinkOnFirstLaunch() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isFirstLaunch = prefs.getBool(_isFirstLaunchKey) ?? true;

      if (isFirstLaunch) {
        // Marca que não é mais o primeiro lançamento
        await prefs.setBool(_isFirstLaunchKey, false);

        // Verifica se há um link diferido pendente
        final deferredLink = await getDeferredLink();
        if (deferredLink != null) {
          AppLogger.logInfo(
            'Link diferido encontrado na primeira execução: $deferredLink',
          );
        }
        return deferredLink;
      }
    } catch (e, stackTrace) {
      AppLogger.logError(
        'Erro ao verificar link diferido na primeira execução',
        e,
        stackTrace,
      );
    }

    return null;
  }

  /// Configura um ID único para o dispositivo
  Future<void> _setupDeviceId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? deviceId = prefs.getString(_deviceIdKey);

      if (deviceId == null) {
        deviceId = await _generateDeviceId();
        await prefs.setString(_deviceIdKey, deviceId);
        AppLogger.logInfo('Novo ID de dispositivo gerado: $deviceId');
      }
    } catch (e, stackTrace) {
      AppLogger.logError(
        'Erro ao configurar ID do dispositivo',
        e,
        stackTrace,
      );
    }
  }

  /// Gera um ID único para o dispositivo
  Future<String> _generateDeviceId() async {
    final deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.id;
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor ??
          DateTime.now().millisecondsSinceEpoch.toString();
    } else {
      return DateTime.now().millisecondsSinceEpoch.toString();
    }
  }

  /// Obtém o ID único do dispositivo
  Future<String> getDeviceId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_deviceIdKey) ?? await _generateDeviceId();
  }

  /// Obtém o link diferido para este dispositivo, se houver
  Future<Map<String, dynamic>?> getDeferredLink() async {
    try {
      final deviceId = await getDeviceId();

      final data =
          await _supabase
              .from(_tabelaDeferredLinks)
              .select()
              .eq('device_id', deviceId)
              .eq('accessed', false)
              .order('created_at', ascending: false)
              .limit(1)
              .maybeSingle();

      if (data != null) {
        // Marcar o link como acessado
        await _supabase
            .from(_tabelaDeferredLinks)
            .update({
              'accessed': true,
              'accessed_at': DateTime.now().toIso8601String(),
            })
            .eq('id', data['id']);

        return data;
      }
    } catch (e, stackTrace) {
      AppLogger.logError(
        'Erro ao buscar link diferido',
        e,
        stackTrace,
      );
    }

    return null;
  }

  /// Cria um novo link diferido
  Future<Map<String, dynamic>?> createDeferredLink({
    required String targetPath,
    Map<String, dynamic>? params,
  }) async {
    try {
      final deviceId = await getDeviceId();

      // Verificar se já existe um link para este device_id
      final existingLink =
          await _supabase
              .from(_tabelaDeferredLinks)
              .select('id')
              .eq('device_id', deviceId)
              .eq('accessed', false)
              .maybeSingle();

      if (existingLink != null) {
        // Atualizar o link existente
        final data =
            await _supabase
                .from(_tabelaDeferredLinks)
                .update({'target_path': targetPath, 'params': params})
                .eq('id', existingLink['id'])
                .select()
                .single();

        AppLogger.logInfo('Link diferido atualizado: $data');
        return data;
      } else {
        // Criar um novo link
        final data =
            await _supabase
                .from(_tabelaDeferredLinks)
                .insert({
                  'device_id': deviceId,
                  'target_path': targetPath,
                  'params': params,
                })
                .select()
                .single();

        AppLogger.logInfo('Novo link diferido criado: $data');
        return data;
      }
    } catch (e, stackTrace) {
      AppLogger.logError(
        'Erro ao criar link diferido',
        e,
        stackTrace,
      );
    }

    return null;
  }

  /// Configura o ouvinte para deep links (comentado)
  // Future<void> _listenToDeepLinks() async {
  //   try {
  //     // Lidar com deep links que abrem o app
  //     _appLinks.uriLinkStream.listen(
  //       (uri) {
  //         _handleDeepLink(uri);
  //       },
  //       onError: (err) {
  //         AppLogger.logError(
  //           'Erro ao processar URI de deep link',
  //           err,
  //           StackTrace.current,
  //         );
  //       },
  //     );

  //     // Lidar com deep links que estavam pendentes na inicialização
  //     final initialUri = await _appLinks.getInitialLink();
  //     if (initialUri != null) {
  //       _handleDeepLink(initialUri);
  //     }
  //   } catch (e, stackTrace) {
  //     AppLogger.logError(
  //       'Erro ao configurar ouvinte de deep links',
  //       e,
  //       stackTrace,
  //     );
  //   }
  // }

  /// Manipula o deep link recebido (desabilitado)
  // void _handleDeepLink(Uri uri) async {
  //   // Função desabilitada temporariamente
  //   AppLogger.logInfo(
  //     '🔗 Deep link recebido, mas o processamento de links diferidos está desabilitado: $uri',
  //   );

  //   // Redireciona para o DeepLinkHandler padrão
  //   try {
  //     final deepLinkHandler = DeepLinkHandler();
  //     await deepLinkHandler.processDeepLink(uri);
  //   } catch (e, stackTrace) {
  //     AppLogger.logError(
  //       'Erro ao redirecionar para o DeepLinkHandler padrão',
  //       e,
  //       stackTrace,
  //     );
  //   }
  // }

  // Future<Product> _getProduct(String id) async {
  //   final int idProduto = int.parse(id);
  //   try {
  //     final data =
  //         await _supabase
  //             .from('produtos_cadastro')
  //             .select()
  //             .eq('id', idProduto)
  //             .select();
  //     final Product produto = Product.fromMap(data.first);
  //     AppLogger.logInfo('Produto recuperado com sucesso: $produto');
  //     return produto;
  //   } catch (e, stackTrace) {
  //     AppLogger.logError('Erro ao buscar produto', e, stackTrace);
  //     rethrow;
  //   }
  // }
}
