import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import 'controllers/categories_controller.dart';
import 'ui/pages/categories_page.dart';
import 'ui/pages/details_category_page.dart';

class CategoriesModule extends Module {
  static const String detailsCategoryPage = '/details_category_page';
  @override
  void binds(i) {
    i.add<CategoriesController>(CategoriesController.new);
  }

  @override
  void routes(r) {
    r.child(
      '/',
      child: (context) => CategoriesPage(),
      transition: TransitionType.scale,
      duration: const Duration(milliseconds: 200),
    );
    r.child(
      detailsCategoryPage,
      child:
          (context) => DetailsCategoryPage(
            key: ValueKey(r.args.data?.id),
            category: r.args.data,
          ),
      transition: TransitionType.fadeIn,
      duration: const Duration(milliseconds: 200),
    );
  }
}
