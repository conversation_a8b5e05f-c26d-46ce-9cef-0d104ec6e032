import 'dart:async';
import 'dart:io';

import 'received_notificationn.dart';
import 'package:firebase_app_installations/firebase_app_installations.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class FirebaseMessagingService extends ChangeNotifier {
  final _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin
  _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  final StreamController<ReceivedNotification>
  didReceiveLocalNotificationStream =
      StreamController<ReceivedNotification>.broadcast();

  Future<void> initNotification() async {
    try {
      await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
      );
      FlutterLocalNotificationsPlugin
      flutterLocalNotificationsPlugin =
          FlutterLocalNotificationsPlugin();
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()
          ?.requestNotificationsPermission();
      setupInteractedMessage();
      initLocalNotifications();
      getToken();
    } catch (e) {
      rethrow;
    }
  }

  Future<void> getInstallationId() async {
    final installations = FirebaseInstallations.instance;
    final id = await installations.getId();
    if (kDebugMode) {
      print('Firebase Installation ID: $id');
    }
  }

  Future<String?> getToken() async {
    try {
      if (Platform.isAndroid) {
        final tokenFirebase = await _firebaseMessaging.getToken();
        if (kDebugMode) {
          print('Token: @@@@@@@@@@@@@@@@@@@@@@@@@');
        }
        if (kDebugMode) {
          print('Token: $tokenFirebase');
        }
        if (kDebugMode) {
          print('Token: @@@@@@@@@@@@@@@@@@@@@@@@@');
        }
        return tokenFirebase;
      }
      if (Platform.isIOS) {
        // final tokenFirebase = await _firebaseMessaging.getToken();
        // if (kDebugMode) {
        //   print('Token: @@@@@@@@@@@@@@@@@@@@@@@@@');
        // }
        // if (kDebugMode) {
        //   print('Token: $tokenFirebase');
        // }
        // if (kDebugMode) {
        //   print('Token: @@@@@@@@@@@@@@@@@@@@@@@@@');
        // }
        // return tokenFirebase;
        if (kDebugMode) {
          final tokenFirebase = await _firebaseMessaging.getToken();
          if (kDebugMode) {
            print('Token: @@@@@@@@@@@@@@@@@@@@@@@@@');
          }
          if (kDebugMode) {
            print('Token: $tokenFirebase');
          }
          if (kDebugMode) {
            print('Token: @@@@@@@@@@@@@@@@@@@@@@@@@');
          }
          return tokenFirebase;
        } else {
          final tokenFirebase = await _firebaseMessaging.getToken();
          if (kDebugMode) {
            print('Token: @@@@@@@@@@@@@@@@@@@@@@@@@');
          }
          if (kDebugMode) {
            print('Token: $tokenFirebase');
          }
          if (kDebugMode) {
            print('Token: @@@@@@@@@@@@@@@@@@@@@@@@@');
          }
          return tokenFirebase;
        }
      }
      return null;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> setupInteractedMessage() async {
    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
          alert: true,
          badge: true,
        );
    // await FirebaseMessaging.instance.subscribeToTopic('PromobellGeral');
    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();

    if (initialMessage != null) {
      _handleMessage(initialMessage);
    }
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      if (message.notification != null) {
        _handleMessage(message);
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);

    if (initialMessage != null) {
      FirebaseMessaging.onBackgroundMessage(_handleMessage);
    }
  }

  Future<void> _handleMessage(RemoteMessage message) async {
    RemoteNotification? notification = message.notification;
    AndroidNotification? android = message.notification?.android;
    if (notification != null && android != null) {
      showNotification(
        ReceivedNotification(
          id: android.hashCode,
          title: notification.title,
          body: notification.body,
          payload: message.data['route'] ?? '',
        ),
      );
    }
  }

  // Adiciona a inicialização do plugin flutter_local_notifications
  Future<void> initLocalNotifications() async {
    try {
      const AndroidInitializationSettings
      initializationSettingsAndroid = AndroidInitializationSettings(
        '@mipmap/ic_launcher',
      );

      final DarwinInitializationSettings
      initializationSettingsDarwin = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestCriticalPermission: true,
        defaultPresentBanner: true,
        defaultPresentBadge: true,
      );

      InitializationSettings initializationSettings =
          InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: initializationSettingsDarwin,
            macOS: initializationSettingsDarwin,
          );

      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<void> showNotification(
    ReceivedNotification receivedNotification,
  ) async {
    AndroidNotificationDetails androidPlatformChannelSpecifics =
        const AndroidNotificationDetails(
          'lembretes_notifications_status_compras',
          'status_compras',
          channelDescription: 'Notificações de status de compras',
          importance: Importance.max,
          priority: Priority.max,
          color: Color(0xFF7FA630),
          enableVibration: true,
          enableLights: true,
          visibility: NotificationVisibility.public,
          largeIcon: DrawableResourceAndroidBitmap(
            '@mipmap/ic_launcher',
          ),
          icon: '@drawable/notification',
          category: AndroidNotificationCategory.alarm,
          channelShowBadge: true,
          colorized: true,
        );

    DarwinNotificationDetails darwinPlatformChannelSpecifics =
        const DarwinNotificationDetails(
          threadIdentifier: 'thread_id',
          presentAlert: true,
          presentBadge: true,
          presentSound: false,
          presentBanner: true,
          interruptionLevel: InterruptionLevel.critical,
          attachments: [],
        );

    NotificationDetails platformChannelSpecifics =
        NotificationDetails(
          android: androidPlatformChannelSpecifics,
          iOS: darwinPlatformChannelSpecifics,
        );

    await _flutterLocalNotificationsPlugin.show(
      receivedNotification.id,
      receivedNotification.title,
      receivedNotification.body,
      platformChannelSpecifics,
      payload: receivedNotification.payload,
    );

    if (kDebugMode) {
      print('Notification showed');
    }
  }
}
