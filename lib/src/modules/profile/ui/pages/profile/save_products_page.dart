import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:promobell/src/components/custom_snack_bar.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/src/core/base/controllers/base_controller.dart/base_controller.dart';
import 'package:promobell/src/models/product.dart';
import 'package:promobell/src/modules/categories/ui/widgets/detail/button_icon_with_background.dart';
import 'package:promobell/src/modules/offers/controllers/offers_controller.dart';
import 'package:promobell/theme/svg_icons.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/custom_pop_scope.dart';
import '../../../../../core/base/widgets/no_connect.dart';
import '../../../../offers/ui/pages/empty_state_save.dart';
import '../../../../offers/ui/widget/offers_page/card_save_product.dart';

class SaveProductsPage extends StatefulWidget {
  const SaveProductsPage({super.key});

  @override
  State<SaveProductsPage> createState() => _SaveProductsPageState();
}

class _SaveProductsPageState extends State<SaveProductsPage> {
  final controller = Modular.get<OffersController>();
  final baseController = Modular.get<BaseController>();
  final ScrollController _scrollController = ScrollController();
  bool _isFirstLoading = true;

  List<Product> localSavedProducts = [];
  final Map<int, GlobalKey<CardSaveProductState>> _cardKeys = {};

  @override
  void initState() {
    super.initState();

    _initializeProducts();

    _scrollController.addListener(() {
      if (!mounted) return;

      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 200 &&
          controller.hasMoreSavedProducts &&
          !controller.isLoadingMoreSaved) {
        controller.loadMoreSavedProducts();
      }
    });
  }

  Future<void> _initializeProducts() async {
    if (!mounted) return;
    await controller.initSavedProducts();
    if (mounted) {
      setState(() {
        localSavedProducts = List.from(controller.savedProducts);
        _isFirstLoading = false; // <- Aqui
      });
    }
  }

  void _handleBack() {
    Modular.to.navigate('/home', arguments: {'initialIndex': 2});
    baseController.navPage(2);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final paddingBottom = MediaQuery.of(context).padding.bottom;

    return CustomPopScope(
      index: 2,
      child: NoConnect(
        child: Scaffold(
          backgroundColor: ColorOutlet.surface,
          body: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(
                  top:
                      Platform.isAndroid
                          ? MediaQuery.of(context).padding.top + 10
                          : MediaQuery.of(context).padding.top,
                  left: 16,
                  right: 16,
                  bottom: 16,
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 40,
                      height: 40,
                      child: ButtonIconWithBackground(
                        iconPath: SvgIcons.arrowClearLeft,
                        onPressed: () => _handleBack(),
                      ),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: TextPattern.customText(
                        text: 'Ofertas salvas',
                        fontSize: 20,
                        fontWeightOption: FontWeightOption.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child:
                      _isFirstLoading
                          ? const Center(
                            child: CircularProgressIndicator(),
                          )
                          : localSavedProducts.isEmpty
                          ? const Padding(
                            padding: EdgeInsets.only(top: 100),
                            child: EmptyStateSave(),
                          )
                          : ListView.builder(
                            controller: _scrollController,
                            padding: const EdgeInsets.only(
                              top: 16,
                              bottom: 32,
                            ),
                            itemCount: localSavedProducts.length + 1,
                            itemBuilder: (context, index) {
                              if (index == 0) {
                                return Padding(
                                  padding: const EdgeInsets.only(
                                    bottom: 16,
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          SvgPicture.asset(
                                            SvgIcons
                                                .markerBookmarkFilled,
                                            colorFilter:
                                                ColorFilter.mode(
                                                  ColorOutlet
                                                      .contentSecondary,
                                                  BlendMode.srcIn,
                                                ),
                                            height: 26,
                                          ),
                                          const SizedBox(width: 8),
                                          TextPattern.customText(
                                            text: 'Salve para depois',
                                            fontSize: 24,
                                            fontWeightOption:
                                                FontWeightOption.bold,
                                            color:
                                                ColorOutlet
                                                    .contentSecondary,
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      TextPattern.customText(
                                        text:
                                            'Salve as ofertas dos produtos que curtir pra encontrar com facilidade quando decidir comprar.',
                                        fontSize: 14,
                                        color:
                                            ColorOutlet
                                                .contentSecondary,
                                      ),
                                    ],
                                  ),
                                );
                              }

                              final productIndex = index - 1;
                              if (productIndex >=
                                  localSavedProducts.length) {
                                return const SizedBox();
                              }

                              final product =
                                  localSavedProducts[productIndex];

                              final key =
                                  GlobalKey<CardSaveProductState>();
                              _cardKeys[product.id] = key;

                              return Padding(
                                padding: const EdgeInsets.only(
                                  bottom: 16,
                                ),
                                child: CardSaveProduct(
                                  key: key,
                                  product: product,
                                  onRemoveConfirmed: () async {
                                    if (!mounted) return;

                                    await controller
                                        .removeSavedProduct(product);

                                    if (context.mounted) {
                                      CustomSnackBar.show(
                                        context: context,
                                        noBottomPadding: true,
                                        message:
                                            "Produto removido da sua lista de ofertas",
                                        icon: SvgIcons.feedbackCheck,
                                      );
                                      setState(() {
                                        localSavedProducts
                                            .removeWhere(
                                              (p) =>
                                                  p.id == product.id,
                                            );
                                        _cardKeys.remove(product.id);
                                      });
                                    }
                                  },
                                ),
                              );
                            },
                          ),
                ),
              ),
              Visibility(
                visible:
                    controller.isLoadingMoreSaved &&
                    controller.hasMoreSavedProducts,
                child: Padding(
                  padding: EdgeInsets.only(
                    bottom: paddingBottom,
                    top: 16,
                  ),
                  child: Lottie.asset(
                    'assets/lottie/lottie-spinner.json',
                    width: 30,
                    height: 30,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
