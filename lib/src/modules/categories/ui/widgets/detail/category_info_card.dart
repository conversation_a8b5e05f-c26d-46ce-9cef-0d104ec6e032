import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/custom_snack_bar.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../offers/controllers/offers_controller.dart';
import '../../../../offers/controllers/story/story_controller.dart';
import 'animated_category_app_bar.dart';
import 'category_name.dart';
import 'follow_button.dart';
import 'outlined_share_button.dart';
import 'text_info_box.dart';

class CategoryInfoCard extends StatelessWidget {
  final AnimatedCategoryAppBar widget;
  final OffersController offersController;

  const CategoryInfoCard({super.key, required this.widget, required this.offersController});

  @override
  Widget build(BuildContext context) {
    final StoryController storyController = Modular.get<StoryController>();
    final followers = offersController.numberLikes(offersController.getFollowersCount(widget.category.id).toString());

    return SingleChildScrollView(
      physics: const NeverScrollableScrollPhysics(),
      child: Padding(
        padding: EdgeInsets.only(left: 16, right: 16, top: widget.controller.totalAppBarHeight + 24),
        child: AnimatedContainer(
          duration: Duration(milliseconds: 100),
          transform: Matrix4.translationValues(0, widget.controller.containerTranslateY, 0),
          child: Stack(
            children: [
              Container(
                height: 288,
                padding: EdgeInsets.only(left: 24, right: 24, bottom: 24, top: 56),
                alignment: Alignment.bottomCenter,
                decoration: BoxDecoration(color: ColorOutlet.paper, borderRadius: BorderRadius.circular(24)),
                child: Opacity(
                  opacity: widget.controller.opacityCard,
                  child: AnimatedContainer(
                    duration: Duration(milliseconds: 100),
                    transform: Matrix4.translationValues(0, widget.controller.columnTranslateY, 0),
                    child: Column(
                      children: [
                        CategoryName(categoria: widget.category),
                        Spacer(),
                        TextPattern.customText(
                          text: widget.category.bio,
                          textAlign: TextAlign.center,
                          fontSize: 14,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Spacer(),
                        Row(
                          children: [
                            Expanded(
                              child: TextInfoBox(text: followers, label: followers == '1' ? 'Seguidor' : 'Seguidores'),
                            ),
                            SizedBox(width: 8),
                            Expanded(
                              child: TextInfoBox(
                                text: widget.length.toString(),
                                label: widget.length == 1 ? 'Oferta' : 'Ofertas',
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: FollowButton(
                                isFollowing: offersController.isCategoryFollowed(widget.category.id),
                                onFollow: () {
                                  offersController.toggleFollowCategory(
                                    widget.category.id,
                                    Supabase.instance.client.auth.currentUser?.email ?? '',
                                    widget.category.nome,
                                  );
                                  storyController.refreshStory();
                                },
                                onUnfollow: () {
                                  offersController.toggleFollowCategory(
                                    widget.category.id,
                                    Supabase.instance.client.auth.currentUser?.email ?? '',
                                    widget.category.nome,
                                  );

                                  storyController.refreshStory();
                                  CustomSnackBar.show(
                                    context: context,
                                    message: "Você deixou de seguir esta categoria.",
                                    icon: SvgIcons.feedbackCheck,
                                  );
                                },
                              ),
                            ),
                            SizedBox(width: 8),
                            Expanded(
                              child: OutlinedShareButton(
                                shared: offersController.isLoadingShare,
                                onPressed: () {
                                  offersController.shareCategory(
                                    widget.category,
                                    isFollowing: offersController.isCategoryFollowed(widget.category.id),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
