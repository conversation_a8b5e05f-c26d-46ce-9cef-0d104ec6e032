import 'package:firebase_core/firebase_core.dart';
import 'package:promobell/flavors/firebase/firebase_options_dev.dart'
    as ios_dev;
import 'package:promobell/flavors/firebase/firebase_options_prod.dart'
    as ios_prod;

import 'firebase_options_dev.dart' as dev_options;
import 'firebase_options_prod.dart' as prod_options;
import 'dart:io' show Platform;

class FirebaseFlavorOptions {
  static FirebaseOptions get dev =>
      Platform.isIOS
          ? ios_dev.DefaultFirebaseOptions.currentPlatform
          : dev_options.DefaultFirebaseOptions.currentPlatform;

  static FirebaseOptions get prod =>
      Platform.isIOS
          ? ios_prod.DefaultFirebaseOptions.currentPlatform
          : prod_options.DefaultFirebaseOptions.currentPlatform;
}


// class FirebaseFlavorOptions {
//   static FirebaseOptions get dev => dev_options.DefaultFirebaseOptions.currentPlatform;
//   static FirebaseOptions get prod => prod_options.DefaultFirebaseOptions.currentPlatform;
// }