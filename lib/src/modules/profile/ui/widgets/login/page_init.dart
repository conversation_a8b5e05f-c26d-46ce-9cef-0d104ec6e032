import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/src/modules/profile/controllers/profile_controller.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/navigation_buttons_row.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/page_view_template.dart';
import 'package:promobell/theme/svg_icons.dart';
import '../../../../../../theme/color_outlet.dart';

class PageInit extends StatefulWidget {
  final VoidCallback onNext;
  final ProfileController controller;

  const PageInit({
    required this.controller,
    required this.onNext,
    super.key,
  });

  @override
  State<PageInit> createState() => _PageInitState();
}

class _PageInitState extends State<PageInit> {
  late TextEditingController nameController;
  late TextEditingController surnameController;
  final nameFocusController = FocusNode();
  final surnameFocusController = FocusNode();
  final formKey = GlobalKey<FormState>();

  void _updateControllerValues() {
    widget.controller.setUserName(
      nameController.text,
      surnameController.text,
    );
  }

  @override
  void initState() {
    nameController = TextEditingController(
      text: widget.controller.userName,
    )..addListener(_updateControllerValues);
    surnameController = TextEditingController(
      text: widget.controller.userSurname,
    )..addListener(_updateControllerValues);
    super.initState();
  }

  @override
  void dispose() {
    nameController.dispose();
    surnameController.dispose();
    nameFocusController.dispose();
    surnameFocusController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final paddingBottom = MediaQuery.of(context).padding.bottom;
    return AnimatedBuilder(
      animation: Listenable.merge([
        nameController,
        surnameController,
        nameFocusController,
        surnameFocusController,
      ]),
      builder: (context, _) {
        return PageViewTemplate(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 160),
              Center(
                child: SvgPicture.asset(
                  SvgIcons.markerVerifiedFilled,
                  colorFilter: ColorFilter.mode(
                    const Color(0xFF7A7AEA),
                    BlendMode.srcIn,
                  ),
                  height: 125,
                ),
              ),
              const SizedBox(height: 32),
              TextPattern.customText(
                text:
                    'Olá!\n${(widget.controller.userName?.trim().isNotEmpty ?? false) ? widget.controller.userName : 'Promolover'}!',
                fontSize: 40,
                fontWeightOption: FontWeightOption.bold,
                textAlign: TextAlign.start,
                color: ColorOutlet.contentPrimary,
              ),

              const SizedBox(height: 32),
              TextPattern.customText(
                text:
                    'Sua conta foi criada, e nada nos deixa mais feliz!',
                fontSize: 24,
                fontWeightOption: FontWeightOption.bold,
                textAlign: TextAlign.start,
              ),
              const SizedBox(height: 32),
              TextPattern.customText(
                text:
                    'Complete seu perfil rapidinho pra deixar tudo com a sua cara enquanto usa o app.',
                fontSize: 16,
                fontWeightOption: FontWeightOption.regular,
                textAlign: TextAlign.start,
                color: ColorOutlet.contentGhost,
              ),
              Spacer(),
              NavigationButtonsRow(
                text: 'Completar perfil',
                onlyButton: true,
                onBack: () {},
                onNext: () {
                  widget.onNext();
                },
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () {
                  widget.controller.updadeDataUser();
                  Modular.to.navigate('/home');
                },
                child: Center(child: Text('Depois')),
              ),
              SizedBox(height: paddingBottom),
            ],
          ),
        );
      },
    );
  }
}
