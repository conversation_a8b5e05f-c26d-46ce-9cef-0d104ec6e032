import 'package:flutter/material.dart';

import '../../../../../../theme/svg_icons.dart';
import '../../../../../core/base/controllers/base_controller.dart/base_controller.dart';
import '../../../../../models/categorias_menu.dart';
import '../../../../../models/product.dart';
import '../../../../../models/story_model.dart';
import '../../../../categories/ui/widgets/detail/button_icon_with_background.dart';
import '../../../controllers/offers_controller.dart';
import '../../../controllers/products_details_controller.dart';
import '../../../controllers/story/story_animation_controller.dart';
import '../../../controllers/story/story_controller.dart';
import '../../../controllers/story/story_navigation_controller.dart';
import 'category_navigator_tile.dart';
import 'story_page_view.dart';
import 'story_timeline.dart';

class StoryContentView extends StatelessWidget {
  final StoryNavigationController navigationController;
  final StoryAnimationController storyAnimationController;
  final List<StoryModel> stories;
  final double topPadding;
  final ProductDetailsController productDetailsController;
  final OffersController controller;
  final StoryController storyController;
  final BaseController controllerBase;
  final CategoriaMenu categoria;
  final VoidCallback onClose;
  final Product product;

  const StoryContentView({
    super.key,
    required this.navigationController,
    required this.storyAnimationController,
    required this.stories,
    required this.topPadding,
    required this.productDetailsController,
    required this.controller,
    required this.storyController,
    required this.controllerBase,
    required this.categoria,
    required this.onClose,
    required this.product,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 10),
      curve: Curves.easeInOut,
      transform: Matrix4.translationValues(
        0,
        storyAnimationController.dragDistance.clamp(0.0, 100.0),
        0,
      ),
      child: Stack(
        children: [
          StoryPageView(
            navigationController: navigationController,
            stories: stories,
            topPadding: topPadding,
            productDetailsController: productDetailsController,
            controller: controller,
            storyController: storyController,
          ),
          AnimatedOpacity(
            opacity: storyAnimationController.headerOpacity,
            duration: const Duration(milliseconds: 300),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  spacing: 16,
                  children: [
                    StoryTimeline(
                      totalStories: stories.length,
                      currentIndex: navigationController.currentIndex,
                      progress: storyAnimationController.progress,
                    ),
                    Align(
                      alignment: Alignment.center,
                      child: Row(
                        children: [
                          CategoryNavigatorTile(
                            controllerBase: controllerBase,
                            categoria: categoria,
                            controller: controller,
                            criadoEm:
                                stories[navigationController
                                        .currentIndex]
                                    .criadoEm,
                            followers: controller.numberLikes(
                              controller
                                  .getFollowersCount(categoria.id)
                                  .toString(),
                            ),
                          ),
                          Spacer(),
                          ButtonIconWithBackground(
                            iconPath: SvgIcons.actionShare,
                            isSared: controller.isLoadingShare,
                            onPressed:
                                () => controller.launchWhatsApp(
                                  product,
                                ),
                          ),
                          SizedBox(width: 8),
                          ButtonIconWithBackground(
                            iconPath: SvgIcons.actionClose,
                            onPressed: onClose,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
