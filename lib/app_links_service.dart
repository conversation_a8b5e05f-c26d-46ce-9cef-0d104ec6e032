import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/models/categorias_menu.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'src/core/base/controllers/base_controller.dart/base_controller.dart';
import 'src/models/product.dart';
import 'src/modules/categories/categories_module.dart';
import 'src/modules/offers/offers_module.dart';
import 'src/services/deep_link_handler.dart';
import 'src/services/logs/app_logger.dart';

class AppLinksService extends ChangeNotifier {
  static bool hasReceivedLink = false;
  final _appLinks = AppLinks();

  final _deepLinkHandler = DeepLinkHandler();

  ValueNotifier<Product> produtos = ValueNotifier<Product>(
    Product.empty(),
  );

  Future<void> initAppLinks() async {
    try {
      hasReceivedLink = false;

      final uri = await _appLinks.getInitialLink();
      if (uri != null && !hasReceivedLink) {
        _handleAppLink(uri);
      }

      _appLinks.uriLinkStream.listen(
        (uri) {
          _handleAppLink(uri);
        },
        onError: (err) {
          AppLogger.logError(
            '❌ AppLinksService: Erro ao processar link',
            err,
            StackTrace.current,
          );
        },
      );
    } catch (e) {
      AppLogger.logError(
        '❌ AppLinksService: Erro ao inicializar serviço de deep links',
        e,
        StackTrace.current,
      );
    }
  }

  void _handleAppLink(Uri uri) async {
    hasReceivedLink = true;

    try {
      String normalizedPath = uri.path.toLowerCase();
      if (normalizedPath.endsWith('/')) {
        normalizedPath = normalizedPath.substring(
          0,
          normalizedPath.length - 1,
        );
      }
      if (normalizedPath.startsWith('/')) {
        normalizedPath = normalizedPath.substring(1);
      }

      if (normalizedPath == 'product') {
        final String? id = uri.queryParameters['id'];
        if (id != null) {
          try {
            final product = await getProduct(id);

            Modular.to.pushNamed(
              '/offers${OffersModule.productDetails}',
              arguments: product,
            );
            return;
          } catch (e) {
            AppLogger.logError(
              '❌ AppLinksService: Erro ao processar link de produto',
              e,
              StackTrace.current,
            );
          }
        }
      } else if (normalizedPath == 'category') {
        final baseController = Modular.get<BaseController>();
        baseController.navPage(1);

        final String? idParam = uri.queryParameters['id'];
        if (idParam != null) {
          final int id = int.tryParse(idParam) ?? -1;
          final categoria = CategoriaMenu.getCategoriaById(id);

          if (categoria != null) {
            Modular.to.navigate(
              '/categories${CategoriesModule.detailsCategoryPage}',
              arguments: categoria,
            );
            return;
          }
        }

        final String? categoryName = uri.queryParameters['name'];
        if (categoryName != null && categoryName.isNotEmpty) {
          try {
            final CategoriaMenu categoriaByName =
                CategoriaMenu.getCategoriaByNome(categoryName);
            Modular.to.navigate(
              '/categories${CategoriesModule.detailsCategoryPage}',
              arguments: categoriaByName,
            );
            return;
          } catch (e) {
            debugPrint('Erro ao buscar categoria por nome: $e');
          }
        }
      }
    } catch (e) {
      AppLogger.logError(
        '❌ AppLinksService: Erro ao processar link de categoria',
        e,
        StackTrace.current,
      );
    }

    await _deepLinkHandler.processDeepLink(uri);
  }

  Future<Product> getProduct(String id) async {
    final int idProduto = int.parse(id);
    final Product product = await getProduto(idProduto);
    return product;
  }

  Future<Product> getProduto(int idProduto) async {
    SupabaseClient supabase = Supabase.instance.client;
    try {
      final data =
          await supabase
              .from('produtos_cadastro')
              .select()
              .eq('id', idProduto)
              .select();
      final Product produto = Product.fromMap(data.first);

      return produto;
    } catch (e, stackTrace) {
      AppLogger.logError('Erro ao buscar produto', e, stackTrace);
      rethrow;
    }
  }
}
