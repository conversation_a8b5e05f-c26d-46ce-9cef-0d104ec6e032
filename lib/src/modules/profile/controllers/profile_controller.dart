import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

import '../../../../firebase_messaging_service.dart';
import '../../../models/user_profile_model.dart';
import '../../../services/digital_ocean/connection/i_storage_connection.dart';
import '../../../services/logs/app_logger.dart';
import '../../../services/supabase/connection/i_connection.dart';
import '../../../services/supabase/db/db.dart';
import '../../../services/supabase/usuarios/delete/delete_usuarios.dart';
import '../../../services/supabase/usuarios/get/get_usuarios.dart';
import '../../../services/supabase/usuarios/put/put_usuarios.dart';

class ProfileController with ChangeNotifier {
  final GetUsuarios _getUsuarios;
  final IStorageConnection _storageConnection;
  final PutUsuarios _putUsuarios;
  UserProfileModel? userProfile;

  ProfileController({
    required GetUsuarios getUsuarios,
    required IStorageConnection storageConnection,
    required PutUsuarios putUsuarios,
  }) : _getUsuarios = getUsuarios,
       _storageConnection = storageConnection,
       _putUsuarios = putUsuarios,
       nameController = TextEditingController(),
       surnameController = TextEditingController(),
       birthDateController = TextEditingController(),

       imageController = TextEditingController() {
    userProfile = UserProfileModel.empty();
  }

  Future<void> loadUserProfile(String email) async {
    try {
      userProfile = await _getUsuarios.getUserByEmail(email);
      if (userProfile != null) {
        saveUserData(userProfile!);
        _populateControllers();
      }

      notifyListeners();
    } catch (e) {
      AppLogger.logError(
        'Erro ao carregar perfil do usuário',
        e,
        StackTrace.current,
      );
      rethrow;
    }
  }

  void setUserName(String name, [String? surname]) {
    userName = name.trim();
    userSurname = surname?.trim() ?? '';

    userProfile ??= UserProfileModel.empty();

    userProfile?.name = getUserName();
    userProfile?.surname = userSurname;
    if (userProfile != null) {
      saveUserData(userProfile!);
    }

    _populateControllers();
    notifyListeners();
  }

  void setDataNascimento(String d, String m, String a) {
    dia = d.trim();
    mes = m.trim();
    ano = a.trim();

    // Apenas armazena os valores sem validação rigorosa durante a digitação
    // A validação completa será feita apenas no momento da submissão
    notifyListeners();
  }

  void finalizarDataNascimento() {
    // Validação inicial
    if (dia.isEmpty ||
        mes.isEmpty ||
        ano.isEmpty ||
        dia == '--' ||
        mes == '--' ||
        ano == '--') {
      AppLogger.logInfo(
        'Data de nascimento incompleta ou inválida. Ignorando parse.',
      );
      return;
    }

    // Garante dois dígitos para dia e mês
    String doisDigitos(String value) => value.padLeft(2, '0');

    // Garante que o ano tem 4 dígitos (ex: 1999, e não 99)
    if (ano.length < 4) {
      AppLogger.logInfo('Ano inválido: $ano');
      return;
    }

    // Monta a data correta
    birthDate =
        '${doisDigitos(ano)}-${doisDigitos(mes)}-${doisDigitos(dia)}';

    userProfile ??= UserProfileModel.empty();

    final parsedDate = DateTime.tryParse(birthDate!);
    if (parsedDate != null) {
      userProfile?.birthDate = parsedDate;
      saveUserData(userProfile!);
      _populateControllers();
      notifyListeners();
    } else {
      AppLogger.logInfo(
        'Data inválida ao tentar converter para DateTime: $birthDate',
      );
    }
  }

  bool setGender() {
    // Validação: verifica se um gênero foi selecionado
    if (gender == null || gender!.trim().isEmpty) {
      AppLogger.logError(
        'Erro: Nenhum gênero foi selecionado',
        'gender é null ou vazio: $gender',
        StackTrace.current,
      );
      return false;
    }

    userProfile ??= UserProfileModel.empty();

    userProfile?.gender = typeGender(gender!);
    if (userProfile != null) {
      saveUserData(userProfile!);
    }

    _populateControllers();
    notifyListeners();
    return true;
  }

  Future<void> updadeDataUser() async {
    finalizarDataNascimento();

    if (birthDate == null) {
      AppLogger.logError(
        'Erro: Data de nascimento não foi definida antes de atualizar usuário',
        'birthDate é null',
        StackTrace.current,
      );
      return;
    }

    final prefs = await SharedPreferences.getInstance();
    await _putUsuarios.updateDataUser(
      email: Supabase.instance.client.auth.currentUser?.email ?? '',
      name: userName ?? prefs.getString('userName') ?? '',
      surname: userSurname ?? prefs.getString('userSurname') ?? '',
      gender: typeGender(gender!),
      birthDate: DateTime.tryParse(birthDate!)!,
    );
  }

  int typeGender(String gender) {
    if (gender == 'Como ela') {
      return 1;
    } else if (gender == 'Como ele') {
      return 2;
    } else {
      return 3;
    }
  }

  String genderFromType(int? genderType) {
    switch (genderType) {
      case 1:
        return 'Como ela';
      case 2:
        return 'Como ele';
      case 3:
        return 'De forma neutra (sem usar ele/ela)';
      default:
        return '';
    }
  }

  void _populateControllers() {
    // Popula o controller de nome
    nameController?.text = userName ?? '';

    // Popula o controller de sobrenome
    surnameController?.text = userSurname ?? '';

    // Popula o controller de data de nascimento
    if (birthDate != null && birthDate!.isNotEmpty) {
      try {
        final parsedDate = DateTime.parse(birthDate!);
        final formattedDate = DateFormat(
          'dd/MM/yyyy',
        ).format(parsedDate);
        birthDateController?.text = formattedDate;
      } catch (e) {
        AppLogger.logError(
          'Erro ao formatar data de nascimento para o controller',
          e,
          StackTrace.current,
        );
        // Se falhar, tenta limpar o campo
        birthDateController?.text = '';
      }
    } else if (userProfile?.birthDate != null) {
      try {
        final formattedDate = DateFormat(
          'dd/MM/yyyy',
        ).format(userProfile!.birthDate!);
        birthDateController?.text = formattedDate;
        birthDate = userProfile!.birthDate!.toIso8601String();
      } catch (e) {
        AppLogger.logError(
          'Erro ao formatar data de nascimento do userProfile para o controller',
          e,
          StackTrace.current,
        );
        birthDateController?.text = '';
      }
    } else {
      birthDateController?.text = '';
    }

    // Popula o controller de gênero
    if (gender != null && gender!.isNotEmpty) {
      genderController.text = gender!;
    } else if (userProfile?.gender != null) {
      final genderString = genderFromType(userProfile!.gender);
      genderController.text = genderString;
      gender = genderString;
    } else {
      genderController.text = '';
    }
  }

  String dia = '';
  String mes = '';
  String ano = '';

  String? userName;
  String? userSurname;
  String? userEmail;
  String? userImage;
  String? userPhone;
  String? userJoinedDate;
  String? gender;
  String? birthDate;

  // Getter para verificar se gênero foi selecionado
  bool get isGenderSelected =>
      gender != null && gender!.trim().isNotEmpty;

  // funcao de limpeza de dados
  void clearUserData() {
    userName = null;
    userSurname = null;
    userEmail = null;
    userImage = null;
    userPhone = null;
    userJoinedDate = null;
    gender = null;
    birthDate = null;
    dia = '';
    mes = '';
    ano = '';

    // Limpa os controllers
    nameController?.clear();
    surnameController?.clear();
    birthDateController?.clear();
    genderController.clear();

    notifyListeners();
  }

  bool _isNullOrEmpty(String? value) {
    return value == null || value.trim().isEmpty;
  }

  Future<void> loadUserData() async {
    final prefs = await SharedPreferences.getInstance();

    // Carrega os dados salvos
    userName = prefs.getString('userName');
    userSurname = prefs.getString('userSurname');
    userEmail = prefs.getString('userEmail');
    userImage = prefs.getString('userImage');
    userPhone = prefs.getString('userPhone');
    userJoinedDate = prefs.getString('userJoinedDate');
    gender = prefs.getString('gender');
    birthDate = prefs.getString('birthDate');

    // Se qualquer um dos dados essenciais estiver nulo ou vazio, buscamos do banco
    final precisaAtualizar =
        _isNullOrEmpty(userEmail) ||
        _isNullOrEmpty(userName) ||
        _isNullOrEmpty(userJoinedDate);

    if (precisaAtualizar) {
      try {
        // Só tenta buscar no banco se tiver um email válido
        final currentUserEmail =
            Supabase.instance.client.auth.currentUser?.email;
        if (currentUserEmail != null && currentUserEmail.isNotEmpty) {
          await loadUserProfile(currentUserEmail);

          if (userProfile != null) {
            await saveUserData(userProfile!);
            userName = userProfile?.name;
            userSurname = userProfile?.surname;
            userEmail = userProfile?.email;
            userImage = userProfile?.image;
            userPhone = userProfile?.phone;
            userJoinedDate =
                userProfile?.joinedDate?.toIso8601String();
            gender = genderFromType(userProfile?.gender);
            birthDate = userProfile?.birthDate?.toIso8601String();

            _populateControllers();
          }
        } else {
          AppLogger.logInfo(
            'Não foi possível carregar dados do banco: usuário não autenticado ou email inválido',
          );
        }
      } catch (e) {
        AppLogger.logError(
          'Erro ao atualizar dados do SharedPreferences com dados do banco',
          e,
          StackTrace.current,
        );
      }
    }

    _populateControllers();
    notifyListeners();
  }

  Future<void> saveUserData(UserProfileModel user) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setString(
      'userName',
      user.name ??
          userEmail?.substring(0, userEmail!.indexOf('@')) ??
          '',
    );
    await prefs.setString('userSurname', user.surname ?? '');
    await prefs.setString('userEmail', user.email ?? '');
    await prefs.setString('userImage', user.image ?? '');
    await prefs.setString('userPhone', user.phone ?? '');
    await prefs.setString(
      'userJoinedDate',
      user.joinedDate?.toIso8601String() ?? '',
    );
    // Só salva gender e birthDate se não forem null
    if (user.gender != null) {
      final genderString = genderFromType(user.gender);
      await prefs.setString('gender', genderString);
    }
    if (user.birthDate != null) {
      final birthDateString = user.birthDate!.toIso8601String();
      await prefs.setString('birthDate', birthDateString);
    }
  }

  Future<void> cleanImageSharedPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('userImage');
    notifyListeners();
  }

  String getUserName() {
    if (userName == null || userName == "") {
      return userEmail?.substring(0, userEmail!.indexOf('@')) ?? '';
    } else {
      return userName!;
    }
  }

  String getSurname() {
    if (userSurname == null || userSurname == "") {
      return '';
    } else {
      return userSurname!;
    }
  }

  String nameAndSurname() {
    return '${getUserName()} ${getSurname()}';
  }

  bool _isEditing = false;
  bool get isEditing => _isEditing;

  void toggleEditing() {
    _isEditing = !_isEditing;
    if (_isEditing) {
      _populateControllers();
    }
    notifyListeners();
  }

  TextEditingController? nameController;
  TextEditingController? surnameController;
  TextEditingController? birthDateController;
  TextEditingController? imageController;
  final TextEditingController genderController =
      TextEditingController();

  final GlobalKey<FormState> formKeyProfile = GlobalKey<FormState>();

  String? validator({
    required String? value,
    required TextEditingController? name,
  }) {
    if ((value == null || value.trim().isEmpty) &&
        (name == null || name.text.trim().isEmpty)) {
      return 'Campo obrigatório!';
    }
    return null;
  }

  String? validateBirthDate(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Campo obrigatório!';
    }

    // Expressão regular: dd/MM/yyyy
    final regex = RegExp(r'^\d{2}/\d{2}/\d{4}$');

    if (!regex.hasMatch(value)) {
      return 'Data deve estar no formato dd/mm/aaaa';
    }

    final parts = value.split('/');

    final day = int.tryParse(parts[0]);
    final month = int.tryParse(parts[1]);
    final year = int.tryParse(parts[2]);

    if (day == null || month == null || year == null) {
      return 'Data inválida';
    }

    if (month < 1 || month > 12) return 'Mês inválido';
    if (day < 1 || day > 31) return 'Dia inválido';

    try {
      final parsedDate = DateTime(year, month, day);

      // Garante que a data realmente exista (ex: 31/02)
      if (parsedDate.day != day ||
          parsedDate.month != month ||
          parsedDate.year != year) {
        return 'Data inválida';
      }

      if (parsedDate.isAfter(DateTime.now())) {
        return 'A data não pode ser futura';
      }

      return null; // ✅ tudo certo
    } catch (_) {
      return 'Data inválida';
    }
  }

  File? imageFile;
  Uint8List? tempImageWeb;
  final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();

  Future<void> pickImage(ImageSource source) async {
    final ImagePicker picker = ImagePicker();

    try {
      if (source == ImageSource.camera) {
        final status = await Permission.camera.request();

        if (status.isPermanentlyDenied) {
          openAppSettings();
          AppLogger.logError(
            'Permissão de câmera negada permanentemente',
            'Permissão',
            StackTrace.current,
          );
          return;
        }

        if (!status.isGranted) {
          AppLogger.logError(
            'Permissão de câmera negada',
            'Permissão',
            StackTrace.current,
          );
          return;
        }
      }

      if (source == ImageSource.gallery) {
        if (Platform.isAndroid) {
          PermissionStatus status;

          final androidVersion = int.parse(
            await deviceInfoPlugin.androidInfo.then(
              (value) => value.version.release,
            ),
          );

          if (androidVersion >= 13) {
            status = await Permission.photos.request();
          } else {
            status = await Permission.storage.request();
          }

          if (status.isPermanentlyDenied) {
            openAppSettings();
            AppLogger.logError(
              'Permissão de galeria negada permanentemente (Android)',
              'Permissão',
              StackTrace.current,
            );
            return;
          }

          if (!status.isGranted) {
            AppLogger.logError(
              'Permissão de galeria negada (Android)',
              'Permissão',
              StackTrace.current,
            );
            return;
          }
        }

        // No iOS: não pedimos permissão manualmente, o image_picker lida com isso
      }

      // Abre a câmera ou galeria
      final XFile? image = await picker.pickImage(source: source);
      if (image != null) {
        final imageBytes = await image.readAsBytes();
        final compressed = await compressToWebP(imageBytes);

        if (compressed != null) {
          tempImageWeb = compressed;
        } else {
          AppLogger.logError(
            'Falha ao comprimir imagem',
            'Compressão',
            StackTrace.current,
          );
        }
      }
    } catch (e) {
      AppLogger.logError(
        'Erro ao carregar imagem',
        e,
        StackTrace.current,
      );
    }

    notifyListeners();
  }

  void cancelEditing() {
    _isEditing = false;
    tempImageWeb = null;
    _populateControllers();

    if (hasListeners) {
      notifyListeners();
    }
  }

  final FirebaseMessagingService firebaseMessagingService =
      FirebaseMessagingService();
  Future<void> updateProfile() async {
    if (!formKeyProfile.currentState!.validate()) return;

    String? imageUrl;
    String? userId;
    if (tempImageWeb != null) {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      userId = const Uuid().v4();

      await _putUsuarios.updateUserImageMapping(userEmail!, userId);

      final path = 'profiles/$userId/$timestamp.webp';

      if (userImage != null) {
        await _storageConnection.deleteFile(userImage!);
      }

      imageUrl = await _storageConnection.uploadFile(
        path,
        tempImageWeb!,
      );
      userImage = imageUrl;
      notifyListeners();
    }
    String token = await firebaseMessagingService.getToken() ?? '';

    final birthDateText = birthDateController?.text ?? '';
    DateTime? parsedBirthDate;
    try {
      final parts = birthDateText.split('/');
      if (parts.length == 3) {
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        parsedBirthDate = DateTime(year, month, day);
      }
    } catch (e) {
      AppLogger.logError(
        'Erro ao converter data de nascimento',
        e,
        StackTrace.current,
      );
    }

    final updatedUser = UserProfileModel(
      name: nameController?.text,
      email: userEmail,
      image: imageUrl ?? userImage,
      imageId: userId ?? userProfile?.imageId,
      tokenFirebase: token,
      surname: surnameController?.text,
      gender: gender != null ? typeGender(gender!) : null,
      birthDate: parsedBirthDate,
    );

    final result = await _putUsuarios.updateUser(updatedUser);
    await saveUserData(result);
    await loadUserData();
    tempImageWeb = null;
    cancelEditing();
    notifyListeners();
  }

  Future<Uint8List?> compressToWebP(Uint8List inputBytes) async {
    final result = await FlutterImageCompress.compressWithList(
      inputBytes,
      quality: 50,
      format: CompressFormat.webp,
      minWidth: 600,
      minHeight: 600,
    );
    return result;
  }

  bool _loadSaving = false;
  bool get loadSaving => _loadSaving;
  set loadSaving(bool value) {
    _loadSaving = value;
    notifyListeners();
  }

  final supabase = Supabase.instance.client;

  bool isCompletedProfile({
    required String name,
    required String gender,
    required String birthDate,
  }) {
    {
      if (name.trim().isEmpty) {
        return false;
      }
      if (gender.trim().isEmpty) {
        return false;
      }
      if (birthDate.trim().isEmpty) {
        return false;
      }
      return true;
    }
  }

  Future<String> getPlataformaAutenticacao(String email) async {
    final userResponse = await supabase.auth.getUser();
    final user = userResponse.user;

    if (user == null) return 'Plataforma desconhecida';

    final provider =
        user.appMetadata['provider'] as String? ?? 'email';
    if (provider == 'email') {
      return _getPlatformByEmail(email);
    }
    switch (provider.toLowerCase()) {
      case 'apple':
        return 'Apple';
      case 'google':
        return 'Google';
      case 'github':
        return 'GitHub';
      case 'facebook':
        return 'Facebook';
      default:
        return 'Outra Plataforma';
    }
  }

  /// Método auxiliar privado que infere a plataforma com base no domínio do e-mail
  String _getPlatformByEmail(String email) {
    email = email.toLowerCase();

    if (email.endsWith('@gmail.com') ||
        email.endsWith('@googlemail.com')) {
      return 'Google';
    }

    if (email.endsWith('@apple.com') ||
        email.endsWith('@icloud.com') ||
        email.endsWith('@me.com') ||
        email.endsWith('@mac.com') ||
        email.contains('privaterelay.appleid.com')) {
      return 'Apple';
    }

    if (email.endsWith('@outlook.com') ||
        email.endsWith('@hotmail.com') ||
        email.endsWith('@live.com') ||
        email.endsWith('@msn.com') ||
        email.endsWith('@passport.com')) {
      return 'Microsoft';
    }

    if (email.endsWith('@yahoo.com') ||
        email.endsWith('@ymail.com') ||
        email.endsWith('@rocketmail.com')) {
      return 'Yahoo';
    }

    if (email.endsWith('@protonmail.com') ||
        email.endsWith('@proton.me')) {
      return 'ProtonMail';
    }

    if (email.endsWith('@zoho.com')) {
      return 'Zoho';
    }

    if (email.endsWith('@yandex.com') ||
        email.endsWith('@yandex.ru')) {
      return 'Yandex';
    }

    return 'Email';
  }

  void resetProfileState() {
    cancelEditing();
    tempImageWeb = null;
    userProfile = null;
    userName = null;
    userEmail = null;
    userImage = null;
    userJoinedDate = null;
    gender = null;
    birthDate = null;

    if (nameController != null) nameController!.text = '';
    if (surnameController != null) surnameController!.text = '';
    if (birthDateController != null) birthDateController!.text = '';
    if (imageController != null) imageController!.text = '';
    genderController.text = '';

    notifyListeners();
  }

  Future<void> deleteUserImage() async {
    try {
      loadSaving = true;

      // 1. Remover o arquivo do storage (DigitalOcean)
      if (userImage != null) {
        final success = await _storageConnection.deleteFile(
          userImage!,
        );
        if (!success) {
          AppLogger.logError(
            'Falha ao excluir imagem no storage',
            'Erro no Storage',
            StackTrace.current,
          );
        }
      }

      // 2. Remover a referência no banco de dados (Supabase)
      if (userEmail != null) {
        final deleteUsuarios = DeleteUsuarios(
          connection: Modular.get<IConnection>(),
          db: Modular.get<DB>(),
        );

        final updatedUser = await deleteUsuarios.removeUserImage(
          userEmail!,
        );

        // 3. Atualizar dados locais
        cleanImageSharedPreferences();
        userImage = null;
        await saveUserData(updatedUser);
        await loadUserData();
      }
    } catch (e) {
      AppLogger.logError(
        'Erro ao excluir imagem do usuário',
        e,
        StackTrace.current,
      );
    } finally {
      loadSaving = false;
    }
  }

  // ONBOARDING

  String? validarMes(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Campo obrigatório.';
    }

    final mes = int.tryParse(value);
    if (mes == null || mes < 1 || mes > 12) {
      return 'Mês inválido.';
    }

    return null;
  }

  String? validarAno(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Campo obrigatório.';
    }

    final ano = int.tryParse(value);
    final anoAtual = DateTime.now().year;

    if (ano == null || ano < 1900 || ano > anoAtual) {
      return 'Ano inválido.';
    }

    return null;
  }

  String? validarDia(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Campo obrigatório.';
    }

    final dia = int.tryParse(value);
    if (dia == null || dia < 1 || dia > 31) {
      return 'Dia inválido.';
    }

    return null;
  }

  String? validarDataCompleta(
    String? diaStr,
    String? mesStr,
    String? anoStr,
  ) {
    final dia = int.tryParse(diaStr ?? '');
    final mes = int.tryParse(mesStr ?? '');
    final ano = int.tryParse(anoStr ?? '');

    if (dia == null || mes == null || ano == null) {
      return 'Preencha uma data válida.';
    }

    try {
      final data = DateTime(ano, mes, dia);
      final hoje = DateTime.now();

      if (data.day != dia || data.month != mes || data.year != ano) {
        return 'Data inválida. Verifique dia, mês e ano.';
      }

      if (data.isAfter(hoje)) {
        return 'A data não pode ser no futuro.';
      }
    } catch (_) {
      return 'Data inválida.';
    }

    return null; // tudo certo
  }

  String? validarCampoNome(String? value) {
    if (value == null || value.isEmpty) {
      return 'Campo obrigatório.';
    }

    String nome = value.trim();

    if (nome.isEmpty) {
      return 'O campo nome não pode ficar vazio.';
    }

    if (nome.length < 2 || nome.length > 30) {
      return 'Nome deve ter entre 2 e 30 caracteres.';
    }

    if (!validarNome(nome)) {
      return 'Nome inválido. Use apenas letras e espaços.';
    }

    return null;
  }

  String? validarCampoSobrenome(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null;
    }

    String sobrenome = value.trim();

    if (sobrenome.length < 2 || sobrenome.length > 30) {
      return 'Sobrenome deve ter entre 2 e 30 caracteres.';
    }

    if (!validarNome(sobrenome)) {
      return 'Sobrenome inválido. Use apenas letras e espaços.';
    }

    return null;
  }

  bool validarNome(String nome) {
    RegExp regex = RegExp(r"^[\p{L}\s'-]+$", unicode: true);
    return regex.hasMatch(nome);
  }
}
